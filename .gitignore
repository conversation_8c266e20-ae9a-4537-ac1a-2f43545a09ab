# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# env files
.env
.env.development
.env.test
.env.production
.env.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# PWA files
**/public/sw.js
**/public/workbox-*.js
**/public/worker-*.js
**/public/sw.js.map
**/public/workbox-*.js.map
**/public/worker-*.js.map
**/public/service-worker.js
**/public/service-worker.js.map
**/public/fallback-*.js
**/public/*.js.map
**/public/precache-manifest.*.js
**/public/precache.*.js
