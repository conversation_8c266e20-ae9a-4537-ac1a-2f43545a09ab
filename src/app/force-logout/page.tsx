"use client";

import usePrivyAuth from "@/hooks/usePrivyAuth";
import { toast } from "@/hooks/useToast";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

const ForceLogout = () => {
  const { confirmLogout } = usePrivyAuth();
  const router = useRouter();

  useEffect(() => {
    confirmLogout(true);
    localStorage.clear();
    router.push("/drop");
    toast({
      title: "Unauthorized, make sure to login again. Or insert your invite code before logging in if you have one.",
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return null;
};

export default ForceLogout;
