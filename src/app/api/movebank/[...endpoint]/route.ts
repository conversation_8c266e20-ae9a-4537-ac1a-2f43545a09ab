import crypto from "crypto";
import { NextRequest, NextResponse } from "next/server";

const MOVEBANK_DIRECT_READ_URL = "https://www.movebank.org/movebank/service/direct-read";
const MOVEBANK_JSON_URL = "https://www.movebank.org/movebank/service/public/json";

const MOVEBANK_USERNAME = process.env.MOVEBANK_USERNAME || "";
const MOVEBANK_PASSWORD = process.env.MOVEBANK_PASSWORD || "";

const acceptedLicenses = new Map<string, string>();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const searchParamsObj = Object.fromEntries(searchParams.entries());
    const studyId = searchParamsObj.study_id;

    const urlObj = new URL(MOVEBANK_JSON_URL);

    searchParams.forEach((value, key) => {
      if (key === "entity_type" && value === "individual") {
      } else {
        urlObj.searchParams.append(key, value);
      }
    });

    if (!urlObj.searchParams.has("sensor_type") && !urlObj.searchParams.has("sensor_type_id")) {
      urlObj.searchParams.append("sensor_type", "gps");
    }

    console.log("Forwarding request to Movebank:", urlObj.toString());

    const authHeader = "Basic " + Buffer.from(`${MOVEBANK_USERNAME}:${MOVEBANK_PASSWORD}`).toString("base64");

    const response = await fetch(urlObj.toString(), {
      headers: {
        Authorization: authHeader,
      },
    });

    const contentType = response.headers.get("content-type");
    const responseText = await response.text();

    if (contentType?.includes("text/html") && responseText.includes("License Terms:")) {
      console.log("Received license terms - need to accept using direct-read endpoint first");

      const directReadUrl = new URL(MOVEBANK_DIRECT_READ_URL);
      directReadUrl.searchParams.append("entity_type", "event");
      directReadUrl.searchParams.append("study_id", studyId);

      const licenseResponse = await fetch(directReadUrl.toString(), {
        headers: {
          Authorization: authHeader,
        },
      });

      if (!licenseResponse.ok) {
        return NextResponse.json(
          { error: "Failed to get license terms", details: await licenseResponse.text() },
          { status: licenseResponse.status },
        );
      }

      const licenseText = await licenseResponse.text();

      const md5Hash = crypto.createHash("md5").update(licenseText).digest("hex");

      if (studyId) {
        acceptedLicenses.set(studyId, md5Hash);
      }

      const acceptLicenseUrl = new URL(directReadUrl.toString());
      acceptLicenseUrl.searchParams.append("license-md5", md5Hash);

      const acceptResponse = await fetch(acceptLicenseUrl.toString(), {
        headers: {
          Authorization: authHeader,
        },
      });

      if (!acceptResponse.ok) {
        return NextResponse.json(
          { error: "Failed to accept license terms", details: await acceptResponse.text() },
          { status: acceptResponse.status },
        );
      }

      const retryResponse = await fetch(urlObj.toString(), {
        headers: {
          Authorization: authHeader,
        },
      });

      if (!retryResponse.ok) {
        return NextResponse.json(
          { error: "Failed to get data after accepting license", details: await retryResponse.text() },
          { status: retryResponse.status },
        );
      }

      try {
        const jsonData = await retryResponse.text();
        return new NextResponse(jsonData, {
          status: 200,
          headers: {
            "Content-Type": "application/json",
          },
        });
      } catch (e) {
        return NextResponse.json(
          { error: "Failed to parse JSON response", details: e instanceof Error ? e.message : String(e) },
          { status: 500 },
        );
      }
    }

    if (response.ok) {
      if (responseText.trim().startsWith("{") && responseText.trim().endsWith("}")) {
        return new NextResponse(responseText, {
          status: 200,
          headers: {
            "Content-Type": "application/json",
          },
        });
      } else {
        return NextResponse.json(
          { error: "Unexpected non-JSON response from Movebank", raw: responseText },
          { status: 500 },
        );
      }
    } else {
      if (response.status === 429) {
        return NextResponse.json(
          {
            error: "Movebank rate limit exceeded",
            details: "The API is temporarily unavailable due to rate limiting. Please try again in a few minutes.",
          },
          {
            status: 429,
            headers: {
              "Retry-After": "60",
            },
          },
        );
      }

      return NextResponse.json(
        {
          error: `Movebank API error: ${response.status}`,
          details: responseText,
        },
        { status: response.status },
      );
    }
  } catch (error) {
    console.error("Error in Movebank API route:", error);
    return NextResponse.json(
      {
        error: "Failed to proxy request to Movebank API",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const studyId = searchParams.get("study_id");

    const urlObj = new URL(MOVEBANK_JSON_URL);

    searchParams.forEach((value, key) => {
      urlObj.searchParams.append(key, value);
    });

    if (!urlObj.searchParams.has("sensor_type") && !urlObj.searchParams.has("sensor_type_id")) {
      urlObj.searchParams.append("sensor_type", "gps");
    }

    const authHeader = "Basic " + Buffer.from(`${MOVEBANK_USERNAME}:${MOVEBANK_PASSWORD}`).toString("base64");

    const response = await fetch(urlObj.toString(), {
      method: "POST",
      headers: {
        Authorization: authHeader,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });

    const contentType = response.headers.get("content-type");
    const responseText = await response.text();

    if (contentType?.includes("text/html") && responseText.includes("License Terms:")) {
      const directReadUrl = new URL(MOVEBANK_DIRECT_READ_URL);
      directReadUrl.searchParams.append("entity_type", "event");
      directReadUrl.searchParams.append("study_id", studyId || "");

      const licenseResponse = await fetch(directReadUrl.toString(), {
        headers: {
          Authorization: authHeader,
        },
      });

      if (!licenseResponse.ok) {
        return NextResponse.json(
          { error: "Failed to get license terms", details: await licenseResponse.text() },
          { status: licenseResponse.status },
        );
      }

      const licenseText = await licenseResponse.text();
      const md5Hash = crypto.createHash("md5").update(licenseText).digest("hex");

      if (studyId) {
        acceptedLicenses.set(studyId, md5Hash);
      }

      const acceptLicenseUrl = new URL(directReadUrl.toString());
      acceptLicenseUrl.searchParams.append("license-md5", md5Hash);

      const acceptResponse = await fetch(acceptLicenseUrl.toString(), {
        headers: {
          Authorization: authHeader,
        },
      });

      if (!acceptResponse.ok) {
        return NextResponse.json(
          { error: "Failed to accept license terms", details: await acceptResponse.text() },
          { status: acceptResponse.status },
        );
      }

      const retryResponse = await fetch(urlObj.toString(), {
        method: "POST",
        headers: {
          Authorization: authHeader,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });

      if (!retryResponse.ok) {
        return NextResponse.json(
          { error: "Failed to get data after accepting license", details: await retryResponse.text() },
          { status: retryResponse.status },
        );
      }

      try {
        const jsonData = await retryResponse.text();
        return new NextResponse(jsonData, {
          status: 200,
          headers: {
            "Content-Type": "application/json",
          },
        });
      } catch (e) {
        return NextResponse.json(
          { error: "Failed to parse JSON response", details: e instanceof Error ? e.message : String(e) },
          { status: 500 },
        );
      }
    }

    if (response.ok) {
      if (responseText.trim().startsWith("{") && responseText.trim().endsWith("}")) {
        return new NextResponse(responseText, {
          status: 200,
          headers: {
            "Content-Type": "application/json",
          },
        });
      } else {
        return NextResponse.json(
          { error: "Unexpected non-JSON response from Movebank", raw: responseText },
          { status: 500 },
        );
      }
    } else {
      if (response.status === 429) {
        return NextResponse.json(
          {
            error: "Movebank rate limit exceeded",
            details: "The API is temporarily unavailable due to rate limiting. Please try again in a few minutes.",
          },
          {
            status: 429,
            headers: {
              "Retry-After": "60",
            },
          },
        );
      }

      return NextResponse.json(
        {
          error: `Movebank API error: ${response.status}`,
          details: responseText,
        },
        { status: response.status },
      );
    }
  } catch (error) {
    return NextResponse.json(
      {
        error: "Failed to proxy request to Movebank API",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
