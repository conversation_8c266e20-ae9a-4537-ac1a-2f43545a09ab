"use client";

import PropertyDetailsModal from "@/components/Bookmark/PropertyDetailsModal";
import IbiIcon from "@/components/IbiUi/IbiIcon";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useClaims } from "@/queries/hooks/useClaims";
import { motion } from "framer-motion";
import Link from "next/link";
import { useState } from "react";

const ClaimsPage = () => {
  const [currentPage, setCurrentPage] = useState(1);
  //   const [selectedClaim, setSelectedClaim] = useState<string | null>(null);
  const [selectedPropertyId, setSelectedPropertyId] = useState<string | null>(null);

  const { claims } = useClaims(currentPage);

  const claimList = claims?.data?.data?.data || [];
  const meta = claims?.data?.data?.meta;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (claims.isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-[#010303]">
        <p className="text-white text-lg">Loading claims...</p>
      </div>
    );
  }

  if (claims.isError) {
    return (
      <div className="h-screen flex items-center justify-center bg-[#010303]">
        <p className="text-red-500 text-lg">Error loading claims. Please try again later.</p>
      </div>
    );
  }

  return (
    <div className="h-screen overflow-y-auto">
      <header className="h-[108px] bg-[#01050dD9] backdrop-blur-md py-2 sticky top-0 z-10 max-[750px]:pt-12">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex items-center justify-between">
            <Link href="/dashboard" className="flex items-center gap-2 flex-col sm:flex-row">
              <IbiIcon icon="ph:file-text" className="text-xl text-white" />
              <h1 className="sm:text-2xl font-bold text-white">Claims</h1>
            </Link>

            <div className="flex items-center gap-4">
              <div className="text-right">
                <p className="text-xs text-gray-400">Total Claims</p>
                <p className="text-lg font-bold text-white">{claimList.length}</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto pt-7 px-4">
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="mb-8">
          <Card className="bg-[#010303]/70 border-gray-800">
            <CardContent className="p-6">
              <div className="overflow-x-auto">
                {claimList.length ? (
                  <>
                    <Table>
                      <TableHeader>
                        <TableRow className="hover:bg-transparent">
                          <TableHead className="text-gray-400">Status</TableHead>
                          <TableHead className="text-gray-400">Property ID</TableHead>
                          <TableHead className="text-gray-400">Created At</TableHead>
                          <TableHead className="text-gray-400">Documents</TableHead>
                          <TableHead className="text-gray-400">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {claimList.map((claim) => (
                          <TableRow key={claim.id} className="hover:bg-[#0a0e15] transition-colors">
                            <TableCell>
                              <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-yellow-500/10 text-yellow-500">
                                <span className="w-1.5 h-1.5 rounded-full bg-yellow-500" />
                                {claim.status || "Unknown"}
                              </span>
                            </TableCell>
                            <TableCell className="font-medium text-white">{claim.id || "N/A"}</TableCell>
                            <TableCell className="text-gray-400">
                              {claim.createdAt ? formatDate(claim.createdAt) : "N/A"}
                            </TableCell>
                            <TableCell>
                              <span className="text-gray-400">
                                {claim.documents ? claim.documents.length : 0} document(s)
                              </span>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                {/* <Button
                                  size="sm"
                                  variant="ghost"
                                  className="text-gray-400 hover:text-blue-400 hover:bg-blue-500/10"
                                  onClick={() => setSelectedClaim(claim.id === selectedClaim ? null : claim.id)}
                                >
                                  <IbiIcon icon="ph:info" className="text-xl" />
                                </Button> */}
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="text-gray-400 hover:text-blue-400 hover:bg-blue-500/10"
                                  onClick={() => setSelectedPropertyId(claim.propertyId || null)}
                                >
                                  <IbiIcon icon="ph:tree" className="text-xl" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>

                    {meta && (
                      <div className="mt-4 flex items-center justify-between">
                        <div className="text-sm text-gray-400">
                          Page {meta.page} of {meta.pageCount}
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                            disabled={!meta.hasPreviousPage}
                          >
                            Previous
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setCurrentPage((p) => p + 1)}
                            disabled={!meta.hasNextPage}
                          >
                            Next
                          </Button>
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="flex flex-col items-center justify-center py-12">
                    <div className="p-4 bg-blue-500/10 rounded-full mb-4">
                      <IbiIcon icon="ph:file-text" className="text-4xl text-blue-400" />
                    </div>
                    <h3 className="text-xl font-medium text-white mb-2">No Claims Yet</h3>
                    <p className="text-gray-400 mb-6">Start by creating your first property claim!</p>
                    <Button>
                      <IbiIcon icon="ph:plus" className="mr-2" />
                      Create Claim
                    </Button>
                  </div>
                )}
              </div>

              {/* <AnimatePresence>
                {selectedClaim && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    className="mt-4 pt-4 border-t border-gray-800"
                  >
                    {claimList
                      .find((claim) => claim.id === selectedClaim)
                      ?.documents?.map((doc) => (
                        <motion.div
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          key={doc.documentId}
                          className="flex items-center justify-between p-3 rounded-lg bg-[#0a0e15] border border-gray-800/30 mb-2"
                        >
                          <div className="flex items-center gap-3">
                            <div className="w-12 h-12 bg-gray-800/50 rounded-lg flex items-center justify-center">
                              <IbiIcon icon="ph:file-text" className="text-2xl text-gray-400" />
                            </div>
                            <div>
                              <h4 className="font-medium text-white">{doc.document?.name || "Unnamed document"}</h4>
                              <p className="text-sm text-gray-400">
                                {doc.document?.size ? (doc.document.size / 1024 / 1024).toFixed(2) : "0.00"} MB
                              </p>
                            </div>
                          </div>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="text-gray-400 hover:text-blue-400 hover:bg-blue-500/10"
                            onClick={() => window.open(doc.document?.url, "_blank")}
                          >
                            <IbiIcon icon="ph:download" className="text-xl" />
                          </Button>
                        </motion.div>
                      ))}
                  </motion.div>
                )}
              </AnimatePresence> */}
            </CardContent>
          </Card>
        </motion.div>
      </div>

      <PropertyDetailsModal propertyId={selectedPropertyId} onClose={() => setSelectedPropertyId(null)} />
    </div>
  );
};

export default ClaimsPage;
