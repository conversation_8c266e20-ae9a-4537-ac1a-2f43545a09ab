"use client";

import PropertyDetailsModal from "@/components/Bookmark/PropertyDetailsModal";
import {
  StyledCard,
  StyledButton,
  StyledTable,
  StyledTableBody,
  StyledTableCell,
  StyledTableHead,
  StyledTableHeader,
  StyledTableRow
} from "@/components/ui/StyledFallbacks";
import { useClaims } from "@/queries/hooks/useClaims";
import Link from "next/link";
import { useState } from "react";

const ClaimsPage = () => {
  const [currentPage, setCurrentPage] = useState(1);
  //   const [selectedClaim, setSelectedClaim] = useState<string | null>(null);
  const [selectedPropertyId, setSelectedPropertyId] = useState<string | null>(null);

  const { claims } = useClaims(currentPage);

  const claimList = claims?.data?.data?.data || [];
  const meta = claims?.data?.data?.meta;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (claims.isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-[#010303]">
        <p className="text-white text-lg">Loading claims...</p>
      </div>
    );
  }

  if (claims.isError) {
    return (
      <div className="h-screen flex items-center justify-center bg-[#010303]">
        <p className="text-red-500 text-lg">Error loading claims. Please try again later.</p>
      </div>
    );
  }

  return (
    <div className="h-screen overflow-y-auto">
      <header className="h-[108px] bg-[#01050dD9] backdrop-blur-md py-2 sticky top-0 z-10 max-[750px]:pt-12">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex items-center justify-between">
            <Link href="/dashboard" className="flex items-center gap-2 flex-col sm:flex-row">
              <span style={{ fontSize: "1.25rem", color: "white" }}>📄</span>
              <h1 className="sm:text-2xl font-bold text-white">Claims</h1>
            </Link>

            <div className="flex items-center gap-4">
              <div className="text-right">
                <p className="text-xs text-gray-400">Total Claims</p>
                <p className="text-lg font-bold text-white">{claimList.length}</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto pt-7 px-4">
        <div className="mb-8">
          <StyledCard className="bg-[#010303]/70 border-gray-800">
            <div style={{ padding: "1.5rem" }}>
              <div className="overflow-x-auto">
                {claimList.length ? (
                  <>
                    <StyledTable>
                      <StyledTableHeader>
                        <StyledTableRow className="hover:bg-transparent">
                          <StyledTableHead className="text-gray-400">Status</StyledTableHead>
                          <StyledTableHead className="text-gray-400">Property ID</StyledTableHead>
                          <StyledTableHead className="text-gray-400">Created At</StyledTableHead>
                          <StyledTableHead className="text-gray-400">Documents</StyledTableHead>
                          <StyledTableHead className="text-gray-400">Actions</StyledTableHead>
                        </StyledTableRow>
                      </StyledTableHeader>
                      <StyledTableBody>
                        {claimList.map((claim) => (
                          <StyledTableRow key={claim.id} className="hover:bg-[#0a0e15] transition-colors">
                            <StyledTableCell>
                              <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-yellow-500/10 text-yellow-500">
                                <span className="w-1.5 h-1.5 rounded-full bg-yellow-500" />
                                {claim.status || "Unknown"}
                              </span>
                            </StyledTableCell>
                            <StyledTableCell className="font-medium text-white">{claim.id || "N/A"}</StyledTableCell>
                            <StyledTableCell className="text-gray-400">
                              {claim.createdAt ? formatDate(claim.createdAt) : "N/A"}
                            </StyledTableCell>
                            <StyledTableCell>
                              <span className="text-gray-400">
                                {claim.documents ? claim.documents.length : 0} document(s)
                              </span>
                            </StyledTableCell>
                            <StyledTableCell>
                              <div className="flex items-center gap-2">
                                <StyledButton
                                  size="sm"
                                  variant="ghost"
                                  className="text-gray-400 hover:text-blue-400 hover:bg-blue-500/10"
                                  onClick={() => setSelectedPropertyId(claim.propertyId || null)}
                                >
                                  🌳
                                </StyledButton>
                              </div>
                            </StyledTableCell>
                          </StyledTableRow>
                        ))}
                      </StyledTableBody>
                    </StyledTable>

                    {meta && (
                      <div className="mt-4 flex items-center justify-between">
                        <div className="text-sm text-gray-400">
                          Page {meta.page} of {meta.pageCount}
                        </div>
                        <div className="flex gap-2">
                          <StyledButton
                            size="sm"
                            variant="outline"
                            onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                            disabled={!meta.hasPreviousPage}
                          >
                            Previous
                          </StyledButton>
                          <StyledButton
                            size="sm"
                            variant="outline"
                            onClick={() => setCurrentPage((p) => p + 1)}
                            disabled={!meta.hasNextPage}
                          >
                            Next
                          </StyledButton>
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="flex flex-col items-center justify-center py-12">
                    <div className="p-4 bg-blue-500/10 rounded-full mb-4">
                      <span style={{ fontSize: "3rem", color: "#60a5fa" }}>📄</span>
                    </div>
                    <h3 className="text-xl font-medium text-white mb-2">No Claims Yet</h3>
                    <p className="text-gray-400 mb-6">Start by creating your first property claim!</p>
                    <StyledButton>
                      Create Claim
                    </StyledButton>
                  </div>
                )}
              </div>

            </div>
          </StyledCard>
        </div>
      </div>

      <PropertyDetailsModal propertyId={selectedPropertyId} onClose={() => setSelectedPropertyId(null)} />
    </div>
  );
};

export default ClaimsPage;
