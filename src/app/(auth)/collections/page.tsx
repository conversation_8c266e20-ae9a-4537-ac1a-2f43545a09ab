"use client";

import PropertyDetailsModal from "@/components/Bookmark/PropertyDetailsModal";
import IbiIcon from "@/components/IbiUi/IbiIcon";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useCollections } from "@/queries/hooks/useCollections";
import { AnimatePresence, motion } from "framer-motion";
import Link from "next/link";
import { useState } from "react";

const CollectionsPage = () => {
  const { collections } = useCollections();
  const [selectedCollection, setSelectedCollection] = useState<string | null>(null);
  const [selectedProperty, setSelectedProperty] = useState<string | null>(null);

  const generalCollection = collections.data?.data.find((c) => c.name === "General");
  const otherCollections = collections.data?.data.filter((c) => c.name !== "General") || [];

  return (
    <div className="h-screen overflow-y-auto">
      <header className="h-[108px] bg-[#01050dD9] backdrop-blur-md py-2 sticky top-0 z-10 max-[750px]:pt-12">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex items-center justify-between">
            <Link href="/dashboard" className="flex items-center gap-2 flex-col sm:flex-row">
              <IbiIcon icon="ph:bookmark-simple-fill" className="text-xl text-white" />
              <h1 className="sm:text-2xl font-bold text-white">Bookmarks</h1>
            </Link>

            <div className="flex items-center gap-4">
              <div className="text-right">
                <p className="text-xs text-gray-400">Total Collections</p>
                <p className="text-lg font-bold text-white">{collections.data?.data.length || 0}</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto pt-7 px-4">
        {generalCollection && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-white mb-4">General Collection</h2>
            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
              <Card
                className={`bg-[#010303]/70 border-gray-800 cursor-pointer transition-all duration-200 hover:bg-[#010303]/80 ${
                  selectedCollection === generalCollection.id ? "ring-2 ring-blue-500/50" : ""
                }`}
                onClick={() =>
                  setSelectedCollection(generalCollection.id === selectedCollection ? null : generalCollection.id)
                }
              >
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-500/20 rounded-lg">
                        <IbiIcon icon="ph:bookmark-simple" className="text-xl text-blue-400" />
                      </div>
                      <div>
                        <h3 className="font-medium text-white">{generalCollection.name}</h3>
                        <p className="text-sm text-gray-400">
                          {generalCollection.properties.length} property
                          {generalCollection.properties.length !== 1 ? "ies" : ""}
                        </p>
                      </div>
                    </div>
                    <Button size="sm" variant="ghost" className="text-gray-400 hover:text-white">
                      <IbiIcon
                        icon={selectedCollection === generalCollection.id ? "ph:caret-up" : "ph:caret-down"}
                        className="text-xl"
                      />
                    </Button>
                  </div>

                  {generalCollection.description && (
                    <p className="text-sm text-gray-400 mb-4">{generalCollection.description}</p>
                  )}

                  <AnimatePresence>
                    {selectedCollection === generalCollection.id && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        exit={{ opacity: 0, height: 0 }}
                        className="space-y-3 mt-4 pt-4 border-t border-gray-800"
                      >
                        {generalCollection.properties.length === 0 ? (
                          <p className="text-center text-gray-500 py-4">No properties yet</p>
                        ) : (
                          generalCollection.properties.map((property) => (
                            <motion.div
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              key={property.id}
                              className="flex items-center justify-between p-3 rounded-lg bg-[#0a0e15] border border-gray-800/30"
                            >
                              <div className="flex items-center gap-3">
                                <div className="w-12 h-12 bg-gray-800/50 rounded-lg flex items-center justify-center">
                                  <IbiIcon icon="ph:tree" className="text-2xl text-gray-400" />
                                </div>
                                <div>
                                  <h4 className="font-medium text-white">Property</h4>
                                </div>
                              </div>
                              <Button
                                size="sm"
                                variant="ghost"
                                className="text-gray-400 hover:text-blue-400 hover:bg-blue-500/10"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedProperty(property.id);
                                }}
                              >
                                <IbiIcon icon="ph:info" className="text-xl" />
                              </Button>
                            </motion.div>
                          ))
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        )}

        <div>
          <h2 className="text-xl font-semibold text-white mb-4">Other Collections</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
            {otherCollections.map((collection) => (
              <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} key={collection.id}>
                <Card
                  className={`bg-[#010303]/50 border-gray-800 cursor-pointer transition-all duration-200 hover:bg-[#010303]/70 ${
                    selectedCollection === collection.id ? "ring-2 ring-blue-500/50" : ""
                  }`}
                  onClick={() => setSelectedCollection(collection.id === selectedCollection ? null : collection.id)}
                >
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-blue-500/10 rounded-lg">
                          <IbiIcon
                            icon={collection.isPrivate ? "ph:lock-simple" : "ph:bookmark-simple"}
                            className="text-xl text-blue-400"
                          />
                        </div>
                        <div>
                          <h3 className="font-medium text-white">{collection.name}</h3>
                          <p className="text-sm text-gray-400">
                            {collection.properties.length} property{collection.properties.length !== 1 ? "ies" : ""}
                          </p>
                        </div>
                      </div>
                      <Button size="sm" variant="ghost" className="text-gray-400 hover:text-white">
                        <IbiIcon
                          icon={selectedCollection === collection.id ? "ph:caret-up" : "ph:caret-down"}
                          className="text-xl"
                        />
                      </Button>
                    </div>

                    {collection.description && <p className="text-sm text-gray-400 mb-4">{collection.description}</p>}

                    <AnimatePresence>
                      {selectedCollection === collection.id && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          className="space-y-3 mt-4 pt-4 border-t border-gray-800"
                        >
                          {collection.properties.length === 0 ? (
                            <p className="text-center text-gray-500 py-4">No properties yet</p>
                          ) : (
                            collection.properties.map((property) => (
                              <motion.div
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                key={property.id}
                                className="flex items-center justify-between p-3 rounded-lg bg-[#0a0e15] border border-gray-800/30"
                              >
                                <div className="flex items-center gap-3">
                                  <div className="w-12 h-12 bg-gray-800/50 rounded-lg flex items-center justify-center">
                                    <IbiIcon icon="ph:tree" className="text-2xl text-gray-400" />
                                  </div>
                                  <div>
                                    <h4 className="font-medium text-white">Property</h4>
                                  </div>
                                </div>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="text-gray-400 hover:text-blue-400 hover:bg-blue-500/10"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setSelectedProperty(property.id);
                                  }}
                                >
                                  <IbiIcon icon="ph:info" className="text-xl" />
                                </Button>
                              </motion.div>
                            ))
                          )}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {collections.data?.data.length === 0 && (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="p-4 bg-blue-500/10 rounded-full mb-4">
                <IbiIcon icon="ph:bookmark-simple" className="text-4xl text-blue-400" />
              </div>
              <h3 className="text-xl font-medium text-white mb-2">No Collections Yet</h3>
              <p className="text-gray-400 mb-6">Start bookmarking properties to create your first collection!</p>
              <Button>
                <IbiIcon icon="ph:plus" className="mr-2" />
                Create Collection
              </Button>
            </div>
          )}
        </div>
      </div>

      <PropertyDetailsModal
        propertyId={selectedProperty}
        collectionId={selectedCollection}
        onClose={() => setSelectedProperty(null)}
      />
    </div>
  );
};

export default CollectionsPage;
