"use client";

import IbiIcon from "@/components/IbiUi/IbiIcon";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";

export default function RewardsPage() {
  return (
    <div className="h-full flex bg-primary-dark text-white">
      <div className="w-80 bg-black/80 border-r border-gray-800 p-6 flex flex-col">
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Avatar className="w-16 h-16">
              <AvatarImage src="/api/placeholder/64/64" />
              <AvatarFallback className="bg-gradient-to-br from-black/90 to-black/60 text-white text-xl font-bold">
                0x
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="text-xl font-bold text-white">0x8012...99b1</h2>
              <p className="text-gray-400 text-sm">Connected Wallet</p>
            </div>
          </div>
        </div>

        <div className="space-y-4 mb-8">
          <div className="bg-[#141414] border border-[#242424] rounded-lg p-3 flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Badge
                variant="secondary"
                className="bg-[#242424] text-white py-2 hover:bg-[#242424]/80 transition-all duration-300"
              >
                <IbiIcon icon="material-symbols:star" className="text-yellow-500 text-xl" />
              </Badge>
              <div className="flex justify-between flex-col ml-2">
                <span className="text-md font-bold">WAVE 1</span>
                <p className="text-xs text-gray-400">Points earned</p>
              </div>
            </div>
            <span className="text-white">0</span>
          </div>

          <div className="bg-[#141414] border border-[#242424] rounded-lg p-3 flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Badge
                variant="secondary"
                className="bg-[#242424] text-white py-2 hover:bg-[#242424]/80 transition-all duration-300"
              >
                <IbiIcon icon="material-symbols:trophy" className="text-purple-500 text-xl" />
              </Badge>
              <div className="flex justify-between flex-col ml-2">
                <span className="text-md font-bold">RANK</span>
                <p className="text-xs text-gray-400">Current position</p>
              </div>
            </div>
            <span className="text-white">0</span>
          </div>

          <div className="bg-[#141414] border border-[#242424] rounded-lg p-3 flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Badge
                variant="secondary"
                className="bg-[#242424] text-white py-2 hover:bg-[#242424]/80 transition-all duration-300"
              >
                <IbiIcon icon="material-symbols:loyalty" className="text-green-500 text-xl" />
              </Badge>
              <div className="flex justify-between flex-col ml-2">
                <span className="text-md font-bold">LOYALTY</span>
                <p className="text-xs text-gray-400">Commitment level</p>
              </div>
            </div>
            <span className="text-white">0</span>
          </div>

          <div className="bg-[#141414] border border-[#242424] rounded-lg p-3 flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Badge
                variant="secondary"
                className="bg-[#242424] text-white py-2 hover:bg-[#242424]/80 transition-all duration-300"
              >
                <IbiIcon icon="material-symbols:package-2" className="text-blue-500 text-xl" />
              </Badge>
              <div className="flex justify-between flex-col ml-2">
                <span className="text-md font-bold">SHIPMENTS OPENED</span>
                <p className="text-xs text-gray-400">Packages claimed</p>
              </div>
            </div>
            <span className="text-white">0</span>
          </div>
        </div>

        <div className="mt-auto">
          <div className="bg-gray-800/50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <IbiIcon icon="material-symbols:refresh" className="text-gray-400" />
              <span className="text-sm text-gray-400">RETRO</span>
            </div>
            <p className="text-xs text-gray-500">
              Rewards for your journey. Recognizing both those who sailed with us, and those who sail onward with us.
            </p>
          </div>
        </div>
      </div>

      <div className="flex-1 flex flex-col">
        <div className="flex-1 p-6 bg-black/80">
          <Tabs defaultValue="leaderboard" className="h-full">
            <TabsList className="grid w-full grid-cols-3 bg-[#141414] border border-[#242424]">
              <TabsTrigger
                value="leaderboard"
                className="data-[state=active]:bg-[#242424] data-[state=active]:text-white"
              >
                Leaderboard
              </TabsTrigger>
              <TabsTrigger
                value="how-it-works"
                className="data-[state=active]:bg-[#242424] data-[state=active]:text-white"
              >
                How it Works?
              </TabsTrigger>
              <TabsTrigger value="rewards" className="data-[state=active]:bg-[#242424] data-[state=active]:text-white">
                Rewards
              </TabsTrigger>
            </TabsList>

            <TabsContent value="leaderboard" className="mt-6 h-[calc(100vh-100px)]">
              <Card className="bg-[#141414] border-[#242424] p-6 h-full">
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <IbiIcon icon="material-symbols:leaderboard" className="text-6xl text-gray-400 mb-4" />
                    <h3 className="text-xl font-semibold text-gray-300 mb-2">Leaderboard Coming Soon</h3>
                    <p className="text-gray-500">Check back soon to see top performers and rankings.</p>
                  </div>
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="how-it-works" className="mt-6 h-[calc(100vh-100px)]">
              <Card className="bg-[#141414] border-[#242424] p-6 h-full">
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <IbiIcon icon="material-symbols:help-center" className="text-6xl text-gray-400 mb-4" />
                    <h3 className="text-xl font-semibold text-gray-300 mb-2">How It Works</h3>
                    <p className="text-gray-500">Learn about our reward system and how to earn points.</p>
                  </div>
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="rewards" className="mt-6 h-[calc(100vh-100px)]">
              <Card className="bg-[#141414] border-[#242424] p-6 h-full">
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <IbiIcon icon="material-symbols:card-giftcard" className="text-6xl text-gray-400 mb-4" />
                    <h3 className="text-xl font-semibold text-gray-300 mb-2">Rewards Center</h3>
                    <p className="text-gray-500">Discover available rewards and claim your earnings.</p>
                  </div>
                </div>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
