import { Button } from "@/components/ui/button";
import React from "react";

const Favorites = () => {
  return (
    <div className="max-w-6xl mx-auto mt-7 px-4 sm:px-6 lg:px-8">
      <h1 className="text-4xl font-bold mb-8">Favorites</h1>
      <div className="w-full h-[580px] flex items-center justify-center flex-col">
        <img
          src="data:image/png;base64,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"
          alt=""
        />
        <h2 className="mt-2 text-2xl font-bold">No favorite assets</h2>
        <p className="text-gray-400 max-w-[400px] text-center">
          Make the most out of YBYCASH by watching assets you’re interested in
        </p>
        <Button className="mt-2">Add asset</Button>
      </div>
    </div>
  );
};

export default Favorites;
