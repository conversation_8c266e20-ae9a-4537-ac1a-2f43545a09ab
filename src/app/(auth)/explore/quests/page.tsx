"use client";

import { Progress } from "@/components/ui/progress";
import { StyledCard } from "@/components/ui/StyledFallbacks";

const quests = [
  {
    id: 1,
    title: "First Trade",
    description: "Complete your first trade on the platform",
    progress: 0,
    reward: "50 XP",
    difficulty: "Easy",
  },
  {
    id: 2,
    title: "Social Butterfly",
    description: "Follow 5 other users",
    progress: 60,
    reward: "100 XP",
    difficulty: "Medium",
  },
  {
    id: 3,
    title: "Hodl Champion",
    description: "Hold an asset for 30 days",
    progress: 33,
    reward: "200 XP",
    difficulty: "Hard",
  },
  {
    id: 4,
    title: "Diversification Master",
    description: "Own 5 different types of assets",
    progress: 80,
    reward: "150 XP",
    difficulty: "Medium",
  },
  {
    id: 5,
    title: "Trading Pro",
    description: "Complete 50 trades",
    progress: 46,
    reward: "300 XP",
    difficulty: "Expert",
  },
  {
    id: 6,
    title: "Early Adopter",
    description: "Be among the first 1000 users",
    progress: 100,
    reward: "500 XP",
    difficulty: "Legendary",
  },
  {
    id: 7,
    title: "Daily Streak",
    description: "Log in for 7 consecutive days",
    progress: 71,
    reward: "100 XP",
    difficulty: "Easy",
  },
  {
    id: 8,
    title: "Rising Star",
    description: "Reach level 10",
    progress: 90,
    reward: "1000 XP",
    difficulty: "Hard",
  },
];

export default function Quests() {
  return (
    <div className="max-w-7xl mx-auto mt-5 px-4 sm:px-6 lg:px-8 max-[750px]:pb-20">
      <h1 className="text-4xl font-bold mb-8 text-white">Quests</h1>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {quests.map((quest) => (
          <StyledCard
            key={quest.id}
            className="bg-slate-900 border-slate-800 text-white transition-all duration-200 hover:bg-slate-800/50 flex flex-col justify-between"
          >
            <div>
              <header className="flex flex-row items-center justify-between space-y-0 pb-2">
                <h1 className="text-sm font-medium">{quest.title}</h1>
              </header>
              <div>
                <p className="text-xs text-slate-400">{quest.description}</p>
                <div className="mt-4 space-y-2">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-slate-400">Progress</span>
                    <span className="font-bold">{quest.progress}%</span>
                  </div>
                  <Progress value={quest.progress} className="h-1 w-full bg-slate-700 dark:bg-slate-600" />
                </div>
              </div>
              <footer className="flex justify-between items-center">
                <div className="bg-slate-800 text-slate-300">
                  {quest.difficulty}
                </div>
                <div className="flex items-center space-x-2 text-xs">
                  <span className="text-slate-400">Reward:</span>
                  <span className="font-bold text-yellow-400">{quest.reward}</span>
                </div>
              </footer>
            </div>
            {quest.progress < 100 && (
              <button className="w-full rounded-t-none bg-blue-600 hover:bg-blue-700 text-white">
                Continue
              </button>
            )}
            {quest.progress === 100 && (
              <button className="w-full rounded-t-none bg-green-600 hover:bg-green-700 text-white">
                Claim Reward
              </button>
            )}
          </StyledCard>
        ))}
      </div>
    </div>
  );
}
