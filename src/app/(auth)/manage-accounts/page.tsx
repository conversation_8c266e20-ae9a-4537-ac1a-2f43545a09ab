"use client";

import IbiConfirmDialog from "@/components/IbiUi/IbiConfirmDialog";
import IbiIcon from "@/components/IbiUi/IbiIcon";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { usePrimaryAccount } from "@/hooks/usePrimaryAccount";
import usePrivyAuth from "@/hooks/usePrivyAuth";
import { cn } from "@/lib/utils";
import { User, usePrivy } from "@privy-io/react-auth";
import Link from "next/link";
import React, { useState } from "react";

const getAccountIcon = (type: string) => {
  const iconMap: { [key: string]: string } = {
    github_oauth: "mdi:github",
    google_oauth: "mdi:google",
    twitter_oauth: "mdi:twitter",
    discord_oauth: "mdi:discord",
    spotify_oauth: "mdi:spotify",
    instagram_oauth: "mdi:instagram",
    tiktok_oauth: "simple-icons:tiktok",
    linkedin_oauth: "mdi:linkedin",
    apple_oauth: "mdi:apple",

    email: "mdi:email",
    phone: "mdi:phone",
    telegram: "mdi:telegram",
    passkey: "material-symbols:passkey",
    custom_auth: "mdi:account-key",

    wallet: "heroicons:wallet-20-solid",
    smart_wallet: "ph:wallet-bold",
    cross_app: "mdi:apps",
  };

  return iconMap[type] || "mdi:account-question";
};

const getAccountDisplayName = (account: any) => {
  switch (account.type) {
    case "wallet":
    case "smart_wallet":
      return account.address ? `${account.address.slice(0, 6)}...${account.address.slice(-4)}` : "Wallet";
    case "email":
      return account.address;
    case "phone":
      return account.number;
    case "github_oauth":
      return `@${account.username}`;
    case "google_oauth":
      return account.email;
    case "twitter_oauth":
      return `@${account.username}`;
    case "discord_oauth":
      return account.username;
    case "spotify_oauth":
      return account.name || account.email;
    case "instagram_oauth":
      return `@${account.username}`;
    case "tiktok_oauth":
      return `@${account.username}`;
    case "linkedin_oauth":
      return account.name || account.email;
    case "apple_oauth":
      return account.email;
    case "telegram":
      return account.username ? `@${account.username}` : account.firstName;
    default:
      return "Connected Account";
  }
};

const getAccountTitle = (account: any) => {
  switch (account.type) {
    case "wallet":
      return "Wallet";
    case "smart_wallet":
      return "Smart Wallet";
    case "email":
      return "Email";
    case "phone":
      return "Phone";
    case "github_oauth":
      return account.name || "GitHub";
    case "google_oauth":
      return "Google";
    case "twitter_oauth":
      return account.name || "Twitter";
    case "discord_oauth":
      return account.name || "Discord";
    case "spotify_oauth":
      return "Spotify";
    case "instagram_oauth":
      return "Instagram";
    case "tiktok_oauth":
      return "TikTok";
    case "linkedin_oauth":
      return account.name || "LinkedIn";
    case "apple_oauth":
      return "Apple";
    case "telegram":
      return account.firstName ? `${account.firstName} ${account.lastName || ""}`.trim() : "Telegram";
    case "passkey":
      return "Passkey";
    case "cross_app":
      return "Cross App";
    default:
      return "Connected Account";
  }
};

interface LinkingMethod {
  name: string;
  icon: string;
  action: () => Promise<void> | void;
  unlinkAction?: (id: string) => Promise<User>;
  enabled: boolean;
  type: string;
}

const ManageAccounts = () => {
  const { user, onLogout } = usePrivyAuth();
  const {
    linkEmail,
    linkPhone,
    linkWallet,
    linkGoogle,
    linkDiscord,
    linkTwitter,
    linkGithub,
    linkLinkedIn,
    linkTiktok,
    linkSpotify,
    linkInstagram,
    linkTelegram,
    linkPasskey,
    linkApple,

    unlinkEmail,
    unlinkPhone,
    unlinkWallet,
    unlinkGoogle,
    unlinkDiscord,
    unlinkTwitter,
    unlinkGithub,
    unlinkLinkedIn,
    unlinkTiktok,
    unlinkSpotify,
    unlinkInstagram,
    unlinkTelegram,
    unlinkPasskey,
    unlinkApple,
  } = usePrivy();

  const [linkingModalOpen, setLinkingModalOpen] = useState(false);
  const [unlinkConfirmOpen, setUnlinkConfirmOpen] = useState(false);
  const [selectedUnlink, setSelectedUnlink] = useState<{ type: string; id: string } | null>(null);

  const linkingMethods: LinkingMethod[] = [
    { name: "Email", icon: "mdi:email", action: linkEmail, unlinkAction: unlinkEmail, enabled: true, type: "email" },
    { name: "Phone", icon: "mdi:phone", action: linkPhone, unlinkAction: unlinkPhone, enabled: true, type: "phone" },
    {
      name: "Wallet",
      icon: "heroicons:wallet-20-solid",
      action: linkWallet,
      unlinkAction: unlinkWallet,
      enabled: true,
      type: "wallet",
    },
    {
      name: "Twitter",
      icon: "mdi:twitter",
      action: linkTwitter,
      unlinkAction: unlinkTwitter,
      enabled: false,
      type: "twitter_oauth",
    },
    {
      name: "GitHub",
      icon: "mdi:github",
      action: linkGithub,
      unlinkAction: unlinkGithub,
      enabled: true,
      type: "github_oauth",
    },
    {
      name: "Discord",
      icon: "mdi:discord",
      action: linkDiscord,
      unlinkAction: unlinkDiscord,
      enabled: true,
      type: "discord_oauth",
    },
    {
      name: "LinkedIn",
      icon: "mdi:linkedin",
      action: linkLinkedIn,
      unlinkAction: unlinkLinkedIn,
      enabled: false,
      type: "linkedin_oauth",
    },
    {
      name: "TikTok",
      icon: "simple-icons:tiktok",
      action: linkTiktok,
      unlinkAction: unlinkTiktok,
      enabled: false,
      type: "tiktok_oauth",
    },
    {
      name: "Spotify",
      icon: "mdi:spotify",
      action: linkSpotify,
      unlinkAction: unlinkSpotify,
      enabled: false,
      type: "spotify_oauth",
    },
    {
      name: "Instagram",
      icon: "mdi:instagram",
      action: linkInstagram,
      unlinkAction: unlinkInstagram,
      enabled: true,
      type: "instagram_oauth",
    },
    {
      name: "Telegram",
      icon: "mdi:telegram",
      action: linkTelegram,
      unlinkAction: unlinkTelegram,
      enabled: false,
      type: "telegram",
    },
    {
      name: "Google",
      icon: "mdi:google",
      action: linkGoogle,
      unlinkAction: unlinkGoogle,
      enabled: true,
      type: "google_oauth",
    },
    {
      name: "Apple",
      icon: "mdi:apple",
      action: linkApple,
      unlinkAction: unlinkApple,
      enabled: false,
      type: "apple_oauth",
    },
    {
      name: "Passkey",
      icon: "material-symbols:passkey",
      action: linkPasskey,
      unlinkAction: unlinkPasskey,
      enabled: true,
      type: "passkey",
    },
  ];

  const handleUnlink = async () => {
    if (!selectedUnlink) return;

    const method = linkingMethods.find((m) => m.type === selectedUnlink.type);
    if (!method?.unlinkAction) return;

    try {
      await method.unlinkAction(selectedUnlink.id);
    } catch (error) {
      console.error("Failed to unlink account:", error);
    }

    setUnlinkConfirmOpen(false);
    setSelectedUnlink(null);
  };

  const initiateUnlink = (type: string, id: string) => {
    setSelectedUnlink({ type, id });
    setUnlinkConfirmOpen(true);
  };

  const getIdForUnlink = (account: any) => {
    switch (account.type) {
      case "email":
        return account.address;
      case "phone":
        return account.number;
      case "wallet":
        return account.address;
      case "telegram":
        return account.telegramUserId;
      case "passkey":
        return account.credentialId;
      default:
        return account.subject;
    }
  };

  const isAccountTypeLinked = (type: string) => {
    if (!user?.linkedAccounts) return false;

    if (type === "wallet") return false;

    const normalizedType = type.endsWith("_oauth") ? type : `${type}_oauth`;

    return user.linkedAccounts.some((account) => {
      if (type === "wallet") {
        return account.type === type || account.type === "smart_wallet";
      }
      return account.type === type || account.type === normalizedType;
    });
  };

  const getAvailableLinkingMethods = () => {
    return linkingMethods.filter((method) => method.enabled && !isAccountTypeLinked(method.type));
  };

  const { displayName, username, avatarLetter } = usePrimaryAccount();

  return (
    <div className="max-[750px]:pt-10">
      <header id="accounts-header" className="h-[108px] bg-[#01050dD9] backdrop-blur-md py-2 sticky top-0 z-10 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between">
            <Link href="/dashboard" className="flex items-center gap-2 flex-col sm:flex-row">
              <IbiIcon icon="tdesign:dashboard-1-filled" className="text-xl text-white" />
              <h1 className="sm:text-2xl font-bold text-white">Manage Accounts</h1>
            </Link>
            <div className="flex items-center gap-4 flex-col-reverse sm:flex-row">
              <div className="text-right">
                <p className="font-medium text-white">{displayName}</p>
                {username && (
                  <p className="text-sm text-gray-300">{username.startsWith("@") ? username : `@${username}`}</p>
                )}
              </div>
              <div className="h-10 w-10 rounded-full bg-blue-950 flex items-center justify-center text-white">
                {avatarLetter}
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto mt-7 px-4">
        <div className="space-y-6">
          <Card className="bg-[#010303]/50 border-gray-800">
            <CardContent className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-white">Linked Accounts</h2>
                <Button
                  className="bg-transparent border border-[#ffffff4b] text-white hover:bg-[#3b83f61f]"
                  onClick={() => setLinkingModalOpen(true)}
                >
                  <IbiIcon icon="mdi:plus" className="mr-2" />
                  Link New Account
                </Button>
              </div>

              <div className="space-y-4">
                {user?.linkedAccounts.map((account, index: number) => (
                  <div key={index} className="flex items-center justify-between p-4 rounded-lg bg-[#010303]/30">
                    <div className="flex items-center gap-3">
                      <IbiIcon icon={getAccountIcon(account.type)} className="text-2xl text-white" />
                      <div>
                        <p className="font-medium text-white">{getAccountTitle(account)}</p>
                        <p className="text-sm text-gray-300">{getAccountDisplayName(account)}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="text-sm text-green-700 font-semibold">Connected</span>
                      {user.linkedAccounts.length > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className={cn(
                            "text-white hover:text-red-300 hover:bg-red-500/10",
                            account.type === "wallet" && "hidden",
                          )}
                          onClick={() => initiateUnlink(account.type, getIdForUnlink(account))}
                        >
                          <IbiIcon icon="heroicons:trash-20-solid" className="text-lg" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <Button
              className="bg-transparent border border-[#ffffff4b] text-white hover:bg-[#f6673b1f]"
              onClick={onLogout}
            >
              <IbiIcon icon="mdi:logout" className="mr-2" />
              Logout
            </Button>
          </div>
        </div>
      </div>

      <Dialog open={linkingModalOpen} onOpenChange={setLinkingModalOpen}>
        <DialogContent className="bg-[#01050dD9] border-gray-800">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold text-white mb-4">Link a New Account</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-gray-300">Connect with one of the following services to link your account.</p>
            {getAvailableLinkingMethods().length === 0 ? (
              <p className="text-gray-400 text-center py-4">You&apos;ve connected all available account types!</p>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {getAvailableLinkingMethods().map((method) => (
                  <Button
                    key={method.name}
                    className="bg-transparent border border-gray-700 hover:bg-[#ffffff0d] text-white flex items-center gap-2 justify-start p-4 h-auto"
                    onClick={() => {
                      method.action();
                      setLinkingModalOpen(false);
                    }}
                  >
                    <IbiIcon icon={method.icon} className="text-xl" />
                    <span>Connect with {method.name}</span>
                  </Button>
                ))}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      <IbiConfirmDialog
        title="Confirm Unlink Account"
        description="Are you sure you want to unlink this account? You can always link it again later."
        open={unlinkConfirmOpen}
        onOpenChange={setUnlinkConfirmOpen}
        onCancel={() => {
          setUnlinkConfirmOpen(false);
          setSelectedUnlink(null);
        }}
        onConfirm={handleUnlink}
      />
    </div>
  );
};

export default ManageAccounts;
