// "use client";

// import { Layer, Map, Source } from "react-map-gl";
// import "mapbox-gl/dist/mapbox-gl.css";
// import { MAPBOX_TOKEN, MAP_PROJECTION } from "@/utils/constants";
// import { useLayerControl } from "@/hooks/useLayerControl";
// import { useMapLayers } from "@/hooks/useMapLayers";
// import useMap from "@/hooks/useMap";
// import MovebankLayer from "@/components/IbiUi/IbiMap/MovebankLayer";
// import { useCallback } from "react";
// import { territoriesService } from "@/services/territories.service";
// import { handleStorage } from "@/utils/storage";
// import { useBaseMap } from "@/hooks/useBaseMap";

// const simulateClickOnLocation = (map: mapboxgl.Map, coordinates: { lng: number; lat: number }) => {
//   const point = map.project(coordinates);
//   const event = new MouseEvent("click", {
//     bubbles: true,
//     cancelable: true,
//     clientX: point.x,
//     clientY: point.y,
//   });
//   map.getCanvas().dispatchEvent(event);

//   const features = map.queryRenderedFeatures(point, { layers: ["properties"] });
//   return features.length > 0;
// };

// export default function World2() {
//   const { mapRef, handleMapLoad } = useLayerControl();
//   const { currentLayers } = useMap();

//   const { forestUrl, alertsUrl, nasaFirmsLayerVisible, fireData, isLoading } = useMapLayers({
//     currentLayers,
//     mapRef: mapRef as React.RefObject<mapboxgl.Map | null>,
//   });

//   const checkLayersLoaded = (map: mapboxgl.Map): Promise<boolean> => {
//     return new Promise((resolve) => {
//       const checkLayers = () => {
//         const propertiesSource = map.getSource("properties-source");
//         if (!propertiesSource) {
//           setTimeout(checkLayers, 100);
//           return;
//         }

//         const propertiesLayer = map.getLayer("properties");
//         if (!propertiesLayer) {
//           setTimeout(checkLayers, 100);
//           return;
//         }

//         if ((propertiesSource as mapboxgl.GeoJSONSource).loaded()) {
//           resolve(true);
//         } else {
//           setTimeout(checkLayers, 100);
//         }
//       };

//       checkLayers();
//     });
//   };

//   const handleShareNavigation = useCallback(async () => {
//     const ibicode = handleStorage<string>("session", "ibiCode", "get");
//     if (!ibicode || !mapRef?.current) return;

//     try {
//       const map = (mapRef?.current as any).getMap();

//       map.dragPan.disable();
//       map.scrollZoom.disable();
//       map.doubleClickZoom.disable();
//       map.touchZoomRotate.disable();
//       map.getCanvas().style.cursor = "not-allowed";

//       const customCoordinates = await territoriesService.getCoordinates(ibicode);

//       map.flyTo({
//         center: [customCoordinates.data.coordinates[0], customCoordinates.data.coordinates[1]],
//         zoom: 12,
//         duration: 600,
//       });

//       await checkLayersLoaded(map);
//       await new Promise((resolve) => setTimeout(resolve, 2000));
//       simulateClickOnLocation(map, {
//         lng: customCoordinates.data.coordinates[0],
//         lat: customCoordinates.data.coordinates[1],
//       });

//       await new Promise((resolve) => setTimeout(resolve, 2000));
//       simulateClickOnLocation(map, {
//         lng: customCoordinates.data.coordinates[0],
//         lat: customCoordinates.data.coordinates[1],
//       });

//       map.dragPan.enable();
//       map.scrollZoom.enable();
//       map.doubleClickZoom.enable();
//       map.touchZoomRotate.enable();
//       map.getCanvas().style.cursor = "";

//       handleStorage("session", "ibiCode", "remove");
//       handleStorage("session", "pathHistory", "remove");
//     } catch (error) {
//       console.error("Error navigating to shared location:", error);

//       if (mapRef?.current) {
//         const map = (mapRef?.current as any).getMap();
//         map.dragPan.enable();
//         map.scrollZoom.enable();
//         map.doubleClickZoom.enable();
//         map.touchZoomRotate.enable();
//         map.getCanvas().style.cursor = "";
//       }

//       handleStorage("session", "ibiCode", "remove");
//       handleStorage("session", "pathHistory", "remove");
//     }
//   }, [mapRef]);

//   const { mapStyle } = useBaseMap();

//   return (
//     <div className="w-full h-full flex flex-col">
//       <Map
//         ref={mapRef as any}
//         mapStyle={mapStyle}
//         initialViewState={{
//           latitude: -14.72,
//           longitude: -51.419,
//           zoom: 4
//         }}
//         mapboxAccessToken={MAPBOX_TOKEN}
//         projection={MAP_PROJECTION}
//         onLoad={handleMapLoad}
//         interactiveLayerIds={['properties']}
//         style={{ width: "100%", height: "100%" }}
//       >
//         {forestUrl && (
//           <Source id="forest-source" type="raster" tiles={[forestUrl]} tileSize={256}>
//             <Layer
//               id="forest-layer"
//               type="raster"
//               paint={{
//                 "raster-opacity": 0.7,
//               }}
//             />
//           </Source>
//         )}

//         {alertsUrl && (
//           <Source id="alerts-source" type="raster" tiles={[alertsUrl]} tileSize={256}>
//             <Layer
//               id="alerts-layer"
//               type="raster"
//               paint={{
//                 "raster-opacity": 0.7,
//               }}
//             />
//           </Source>
//         )}

//         {currentLayers.includes("movebank") && <MovebankLayer visible={true} pointColor="#FF9800" pointSize={5} />}

//         {nasaFirmsLayerVisible && fireData && (
//           <Source id="fire-data" type="geojson" data={fireData}>
//             <Layer
//               id="fire-layer"
//               type="circle"
//               paint={{
//                 "circle-radius": 5,
//                 "circle-color": "#ff0000",
//                 "circle-stroke-width": 1,
//                 "circle-stroke-color": "#ffffff",
//               }}
//             />
//           </Source>
//         )}
//       </Map>
//     </div>
//   );
// }

export default function World2() {
  return <div>World2</div>;
}
