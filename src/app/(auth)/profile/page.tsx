"use client";

import {
  Styled<PERSON><PERSON><PERSON>,
  Styled<PERSON><PERSON><PERSON>,
  Styled<PERSON><PERSON>,
  StyledCard<PERSON>ontent,
  StyledCardHeader,
  StyledCardTitle,
  StyledTable,
  StyledTableBody,
  StyledTableCell,
  StyledTableHead,
  StyledTableHeader,
  StyledTableRow
} from "@/components/ui/StyledFallbacks";
import Link from "next/link";

const user = {
  name: "<PERSON>",
  totalLandUnits: 5,
  totalTokens: 12500,
  totalValue: 1875.0,
  monthlyEarnings: 125.0,
};
const landUnits = [
  {
    id: 1,
    name: "Amazon Rainforest",
    size: 150,
    type: "Tropical Forest",
    resources: ["Wood", "Medicinal Plants"],
    tokenPrice: 0.05,
    accumulatedTokens: 2500,
    icon: Tree,
  },
  {
    id: 2,
    name: "Serra da Mantiqueira",
    size: 200,
    type: "Mountain",
    resources: ["Ecotourism", "Mineral Water"],
    tokenPrice: 0.1,
    accumulatedTokens: 1500,
    icon: Mountain,
  },
  {
    id: 3,
    name: "Pan<PERSON><PERSON>",
    size: 100,
    type: "Wetland",
    resources: ["Biodiversity", "Fishing"],
    tokenPrice: 0.08,
    accumulatedTokens: 3000,
    icon: Droplets,
  },
  {
    id: 4,
    name: "Ce<PERSON><PERSON>",
    size: 300,
    type: "Savanna",
    resources: ["Native Fruits", "Medicinal Plants"],
    tokenPrice: 0.15,
    accumulatedTokens: 2000,
    icon: Sun,
  },
  {
    id: 5,
    name: "Chapada Diamantina",
    size: 180,
    type: "Plateau",
    resources: ["Minerals", "Adventure Tourism"],
    tokenPrice: 0.07,
    accumulatedTokens: 1800,
    icon: Wind,
  },
];
export default function Profile() {
  return (
    <section className="w-full h-screen overflow-y-auto max-[750px]:pb-20 px-4">
      <header
        id="dashboard-header"
        className="h-auto min-h-[108px] px-4 sm:px-6 bg-[#01050dD9] backdrop-blur-md py-4 sticky top-0 z-10 max-[750px]:pt-10"
      >
        <div className="h-full max-w-7xl mx-auto flex justify-between flex-col">
          <Link href="/dashboard" className="flex items-center gap-2">
            <span style={{ fontSize: "1.25rem" }}>📊</span>
            <h1 className="text-xl sm:text-2xl font-bold">Profile</h1>
          </Link>
        </div>
      </header>
      <div className="max-w-7xl mx-auto mt-4 sm:mt-7 px-2 sm:px-4 lg:px-8">
        <h1 className="text-2xl sm:text-3xl font-bold mb-4 sm:mb-8 text-white">Dashboard</h1>

        <div className="flex gap-2 mb-8 max-[550px]:flex-wrap max-[550px]:w-full">
          <StyledCard className="bg-slate-800 border-slate-700 text-white w-full max-w-[320px] max-[550px]:max-w-[420px]">
            <StyledCardHeader className="flex flex-row items-center justify-between p-3 sm:pb-2 space-y-0">
              <StyledCardTitle className="text-xs sm:text-sm font-medium">Total Land Units</StyledCardTitle>
              <span style={{ fontSize: "0.875rem", color: "#9ca3af" }}>🗺️</span>
            </StyledCardHeader>
            <StyledCardContent className="p-3 pt-1 sm:pt-2">
              <div className="text-lg sm:text-2xl font-bold">{user.totalLandUnits}</div>
            </StyledCardContent>
          </StyledCard>
          <StyledCard className="bg-slate-800 border-slate-700 text-white w-full max-w-[320px] max-[550px]:max-w-[420px]">
            <StyledCardHeader className="flex flex-row items-center justify-between p-3 sm:pb-2 space-y-0">
              <StyledCardTitle className="text-xs sm:text-sm font-medium">Total Tokens</StyledCardTitle>
              <span style={{ fontSize: "0.875rem", color: "#9ca3af" }}>🪙</span>
            </StyledCardHeader>
            <StyledCardContent className="p-3 pt-1 sm:pt-2">
              <div className="text-lg sm:text-2xl font-bold">{user.totalTokens.toLocaleString()}</div>
            </StyledCardContent>
          </StyledCard>
          <StyledCard className="bg-slate-800 border-slate-700 text-white w-full max-w-[320px] max-[550px]:max-w-[420px]">
            <StyledCardHeader className="flex flex-row items-center justify-between p-3 sm:pb-2 space-y-0">
              <StyledCardTitle className="text-xs sm:text-sm font-medium">Total Value</StyledCardTitle>
              <span style={{ fontSize: "0.875rem", color: "#9ca3af" }}>📈</span>
            </StyledCardHeader>
            <StyledCardContent className="p-3 pt-1 sm:pt-2">
              <div className="text-lg sm:text-2xl font-bold">$ {user.totalValue.toFixed(2)}</div>
            </StyledCardContent>
          </StyledCard>
          <StyledCard className="bg-slate-800 border-slate-700 text-white w-full max-w-[320px] max-[550px]:max-w-[420px]">
            <StyledCardHeader className="flex flex-row items-center justify-between p-3 sm:pb-2 space-y-0">
              <StyledCardTitle className="text-xs sm:text-sm font-medium">Monthly Earnings</StyledCardTitle>
              <span style={{ fontSize: "0.875rem", color: "#9ca3af" }}>📊</span>
            </StyledCardHeader>
            <StyledCardContent className="p-3 pt-1 sm:pt-2">
              <div className="text-lg sm:text-2xl font-bold">$ {user.monthlyEarnings.toFixed(2)}</div>
            </StyledCardContent>
          </StyledCard>
        </div>

        <div className="max-[550px]:overflow-x-auto max-[550px]:w-[300px]">
          <StyledCard className="bg-slate-900 border-slate-700 text-white overflow-x-auto">
            <StyledCardHeader>
              <StyledCardTitle className="text-lg sm:text-xl">Your Land Units</StyledCardTitle>
            </StyledCardHeader>
            <StyledCardContent className="p-2 sm:p-4">
              <div className="overflow-x-auto">
                <StyledTable>
                  <StyledTableHeader>
                    <StyledTableRow>
                      <StyledTableHead className="text-white text-xs sm:text-sm">Name</StyledTableHead>
                      <StyledTableHead className="text-white text-xs sm:text-sm">Type</StyledTableHead>
                      <StyledTableHead className="text-white text-xs sm:text-sm">Size (ha)</StyledTableHead>
                      <StyledTableHead className="text-white text-xs sm:text-sm">Tokens</StyledTableHead>
                      <StyledTableHead className="text-white text-xs sm:text-sm">Value ($)</StyledTableHead>
                      <StyledTableHead className="text-white text-xs sm:text-sm">Actions</StyledTableHead>
                    </StyledTableRow>
                  </StyledTableHeader>
                  <StyledTableBody>
                    {landUnits.map((unit) => (
                      <StyledTableRow key={unit.id}>
                        <StyledTableCell className="font-medium text-xs sm:text-sm">{unit.name}</StyledTableCell>
                        <StyledTableCell className="text-xs sm:text-sm">{unit.type}</StyledTableCell>
                        <StyledTableCell className="text-xs sm:text-sm">{unit.size}</StyledTableCell>
                        <StyledTableCell className="text-xs sm:text-sm">{unit.accumulatedTokens.toLocaleString()}</StyledTableCell>
                        <StyledTableCell className="text-xs sm:text-sm">
                          {(unit.accumulatedTokens * unit.tokenPrice * 5).toFixed(2)}
                        </StyledTableCell>
                        <StyledTableCell>
                          <StyledButton
                            variant="outline"
                            size="sm"
                            className="border-slate-700 hover:bg-slate-800 text-gray-800 hover:text-white text-xs sm:text-sm px-2 py-1"
                          >
                            Details ↗
                          </StyledButton>
                        </StyledTableCell>
                      </StyledTableRow>
                    ))}
                  </StyledTableBody>
                </StyledTable>
              </div>
            </StyledCardContent>
          </StyledCard>
        </div>

        <div className="mt-8 flex gap-2 mb-8 max-[550px]:flex-wrap max-[550px]:w-full">
          <StyledCard className="bg-slate-800 border-slate-700 text-white w-full max-w-[320px] max-[550px]:max-w-[420px]">
            <StyledCardHeader className="p-3 sm:p-4">
              <StyledCardTitle className="text-base sm:text-lg">Most Valuable Resources</StyledCardTitle>
            </StyledCardHeader>
            <StyledCardContent className="p-3 sm:p-4">
              <div className="space-y-2 sm:space-y-4">
                {["Wood", "Ecotourism", "Biodiversity"].map((resource, index) => (
                  <div key={index} className="flex items-center">
                    <StyledBadge variant="secondary" className="mr-2 bg-emerald-900 text-emerald-100 text-xs sm:text-sm">
                      {index + 1}
                    </StyledBadge>
                    <span className="text-xs sm:text-sm">{resource}</span>
                  </div>
                ))}
              </div>
            </StyledCardContent>
          </StyledCard>
          <StyledCard className="bg-slate-800 border-slate-700 text-white w-full max-w-[320px] max-[550px]:max-w-[420px]">
            <StyledCardHeader className="p-3 sm:p-4">
              <StyledCardTitle className="text-base sm:text-lg">Monthly Performance</StyledCardTitle>
            </StyledCardHeader>
            <StyledCardContent className="p-3 sm:p-4">
              <div className="text-2xl font-bold text-green-400">+5.2%</div>
              <p className="text-sm text-slate-400">Compared to previous month</p>
            </StyledCardContent>
          </StyledCard>
          <StyledCard className="bg-slate-800 border-slate-700 text-white w-full max-w-[320px] max-[550px]:max-w-[420px]">
            <StyledCardHeader className="p-3 sm:p-4">
              <StyledCardTitle className="text-base sm:text-lg">Next Distribution</StyledCardTitle>
            </StyledCardHeader>
            <StyledCardContent className="p-3 sm:p-4">
              <div className="text-2xl font-bold">15 days</div>
              <p className="text-sm text-slate-400">Estimated: $ 150.00</p>
            </StyledCardContent>
          </StyledCard>
        </div>
      </div>
    </section>
  );
}
