"use client";

import IbiIcon from "@/components/IbiUi/IbiIcon";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  Activity,
  ArrowUpRight,
  Coins,
  Droplets,
  Map,
  Mountain,
  Sun,
  TreesIcon as Tree,
  TrendingUp,
  Wind,
} from "lucide-react";
import Link from "next/link";

const user = {
  name: "<PERSON>",
  totalLandUnits: 5,
  totalTokens: 12500,
  totalValue: 1875.0,
  monthlyEarnings: 125.0,
};
const landUnits = [
  {
    id: 1,
    name: "Amazon Rainforest",
    size: 150,
    type: "Tropical Forest",
    resources: ["Wood", "Medicinal Plants"],
    tokenPrice: 0.05,
    accumulatedTokens: 2500,
    icon: Tree,
  },
  {
    id: 2,
    name: "<PERSON> da Mantiqueira",
    size: 200,
    type: "Mountain",
    resources: ["Ecotourism", "Mineral Water"],
    tokenPrice: 0.1,
    accumulatedTokens: 1500,
    icon: Mountain,
  },
  {
    id: 3,
    name: "Pantanal",
    size: 100,
    type: "Wetland",
    resources: ["Biodiversity", "Fishing"],
    tokenPrice: 0.08,
    accumulatedTokens: 3000,
    icon: Droplets,
  },
  {
    id: 4,
    name: "Cerrado",
    size: 300,
    type: "Savanna",
    resources: ["Native Fruits", "Medicinal Plants"],
    tokenPrice: 0.15,
    accumulatedTokens: 2000,
    icon: Sun,
  },
  {
    id: 5,
    name: "Chapada Diamantina",
    size: 180,
    type: "Plateau",
    resources: ["Minerals", "Adventure Tourism"],
    tokenPrice: 0.07,
    accumulatedTokens: 1800,
    icon: Wind,
  },
];
export default function Profile() {
  return (
    <section className="w-full h-screen overflow-y-auto max-[750px]:pb-20 px-4">
      <header
        id="dashboard-header"
        className="h-auto min-h-[108px] px-4 sm:px-6 bg-[#01050dD9] backdrop-blur-md py-4 sticky top-0 z-10 max-[750px]:pt-10"
      >
        <div className="h-full max-w-7xl mx-auto flex justify-between flex-col">
          <Link href="/dashboard" className="flex items-center gap-2">
            <IbiIcon icon="tdesign:dashboard-1-filled" className="text-xl" />
            <h1 className="text-xl sm:text-2xl font-bold">Profile</h1>
          </Link>
        </div>
      </header>
      <div className="max-w-7xl mx-auto mt-4 sm:mt-7 px-2 sm:px-4 lg:px-8">
        <h1 className="text-2xl sm:text-3xl font-bold mb-4 sm:mb-8 text-white">Dashboard</h1>

        <div className="flex gap-2 mb-8 max-[550px]:flex-wrap max-[550px]:w-full">
          <Card className="bg-slate-800 border-slate-700 text-white w-full max-w-[320px] max-[550px]:max-w-[420px]">
            <CardHeader className="flex flex-row items-center justify-between p-3 sm:pb-2 space-y-0">
              <CardTitle className="text-xs sm:text-sm font-medium">Total Land Units</CardTitle>
              <Map className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="p-3 pt-1 sm:pt-2">
              <div className="text-lg sm:text-2xl font-bold">{user.totalLandUnits}</div>
            </CardContent>
          </Card>
          <Card className="bg-slate-800 border-slate-700 text-white w-full max-w-[320px] max-[550px]:max-w-[420px]">
            <CardHeader className="flex flex-row items-center justify-between p-3 sm:pb-2 space-y-0">
              <CardTitle className="text-xs sm:text-sm font-medium">Total Tokens</CardTitle>
              <Coins className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="p-3 pt-1 sm:pt-2">
              <div className="text-lg sm:text-2xl font-bold">{user.totalTokens.toLocaleString()}</div>
            </CardContent>
          </Card>
          <Card className="bg-slate-800 border-slate-700 text-white w-full max-w-[320px] max-[550px]:max-w-[420px]">
            <CardHeader className="flex flex-row items-center justify-between p-3 sm:pb-2 space-y-0">
              <CardTitle className="text-xs sm:text-sm font-medium">Total Value</CardTitle>
              <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="p-3 pt-1 sm:pt-2">
              <div className="text-lg sm:text-2xl font-bold">$ {user.totalValue.toFixed(2)}</div>
            </CardContent>
          </Card>
          <Card className="bg-slate-800 border-slate-700 text-white w-full max-w-[320px] max-[550px]:max-w-[420px]">
            <CardHeader className="flex flex-row items-center justify-between p-3 sm:pb-2 space-y-0">
              <CardTitle className="text-xs sm:text-sm font-medium">Monthly Earnings</CardTitle>
              <Activity className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="p-3 pt-1 sm:pt-2">
              <div className="text-lg sm:text-2xl font-bold">$ {user.monthlyEarnings.toFixed(2)}</div>
            </CardContent>
          </Card>
        </div>

        <div className="max-[550px]:overflow-x-auto max-[550px]:w-[300px]">
          <Card className="bg-slate-900 border-slate-700 text-white overflow-x-auto">
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">Your Land Units</CardTitle>
            </CardHeader>
            <CardContent className="p-2 sm:p-4">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-white text-xs sm:text-sm">Name</TableHead>
                      <TableHead className="text-white text-xs sm:text-sm">Type</TableHead>
                      <TableHead className="text-white text-xs sm:text-sm">Size (ha)</TableHead>
                      <TableHead className="text-white text-xs sm:text-sm">Tokens</TableHead>
                      <TableHead className="text-white text-xs sm:text-sm">Value ($)</TableHead>
                      <TableHead className="text-white text-xs sm:text-sm">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {landUnits.map((unit) => (
                      <TableRow key={unit.id}>
                        <TableCell className="font-medium text-xs sm:text-sm">{unit.name}</TableCell>
                        <TableCell className="text-xs sm:text-sm">{unit.type}</TableCell>
                        <TableCell className="text-xs sm:text-sm">{unit.size}</TableCell>
                        <TableCell className="text-xs sm:text-sm">{unit.accumulatedTokens.toLocaleString()}</TableCell>
                        <TableCell className="text-xs sm:text-sm">
                          {(unit.accumulatedTokens * unit.tokenPrice * 5).toFixed(2)}
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-slate-700 hover:bg-slate-800 text-gray-800 hover:text-white text-xs sm:text-sm px-2 py-1"
                          >
                            Details
                            <ArrowUpRight className="ml-1 h-3 w-3 sm:h-4 sm:w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="mt-8 flex gap-2 mb-8 max-[550px]:flex-wrap max-[550px]:w-full">
          <Card className="bg-slate-800 border-slate-700 text-white w-full max-w-[320px] max-[550px]:max-w-[420px]">
            <CardHeader className="p-3 sm:p-4">
              <CardTitle className="text-base sm:text-lg">Most Valuable Resources</CardTitle>
            </CardHeader>
            <CardContent className="p-3 sm:p-4">
              <div className="space-y-2 sm:space-y-4">
                {["Wood", "Ecotourism", "Biodiversity"].map((resource, index) => (
                  <div key={index} className="flex items-center">
                    <Badge variant="secondary" className="mr-2 bg-emerald-900 text-emerald-100 text-xs sm:text-sm">
                      {index + 1}
                    </Badge>
                    <span className="text-xs sm:text-sm">{resource}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          <Card className="bg-slate-800 border-slate-700 text-white w-full max-w-[320px] max-[550px]:max-w-[420px]">
            <CardHeader className="p-3 sm:p-4">
              <CardTitle className="text-base sm:text-lg">Monthly Performance</CardTitle>
            </CardHeader>
            <CardContent className="p-3 sm:p-4">
              <div className="text-2xl font-bold text-green-400">+5.2%</div>
              <p className="text-sm text-slate-400">Compared to previous month</p>
            </CardContent>
          </Card>
          <Card className="bg-slate-800 border-slate-700 text-white w-full max-w-[320px] max-[550px]:max-w-[420px]">
            <CardHeader className="p-3 sm:p-4">
              <CardTitle className="text-base sm:text-lg">Next Distribution</CardTitle>
            </CardHeader>
            <CardContent className="p-3 sm:p-4">
              <div className="text-2xl font-bold">15 days</div>
              <p className="text-sm text-slate-400">Estimated: $ 150.00</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
