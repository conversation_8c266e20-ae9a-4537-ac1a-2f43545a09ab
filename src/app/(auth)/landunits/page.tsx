"use client";

import { LandUnitsBondingCurve } from "@/components/LandUnits/BondingCurve";
import { LandUnitsClaims } from "@/components/LandUnits/Claims";
import { LandUnitsCollections } from "@/components/LandUnits/Collections";
import { StyledButton, StyledTabs, StyledTabsContent, StyledTabsList, StyledTabsTrigger } from "@/components/ui/StyledFallbacks";
import React, { useState } from "react";

export default function LandUnits() {
  const [activeTab, setActiveTab] = useState("collections");

  return (
    <section className="w-full h-screen overflow-y-auto max-[750px]:pb-20">
      <header
        id="dashboard-header"
        className="h-auto min-h-[108px] bg-gradient-to-b from-[#01050dF9] to-[#01050dD9] backdrop-blur-md py-6 sticky top-0 z-10 max-[750px]:pt-10 border-b border-slate-800/50 max-[1280px]:px-4"
      >
        <div className="h-full max-w-7xl mx-auto flex justify-between flex-col">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-slate-800/50 rounded-lg">
                <span style={{ fontSize: "1.25rem", color: "#10b981" }}>📊</span>
              </div>
              <h1 className="text-xl sm:text-2xl font-bold">Land Units</h1>
            </div>
            <StyledButton size="sm" className="gap-2">
              <span>🗺️</span>
              <span>Browse map</span>
            </StyledButton>
          </div>
        </div>
      </header>

      <div className="max-w-7xl max-[1280px]:px-4 mx-auto mt-4 sm:mt-7">
        <StyledTabs className="w-full">
          <StyledTabsList className="bg-slate-800/50 mb-4">
            <StyledTabsTrigger
              value="collections"
              active={activeTab === "collections"}
              onClick={() => setActiveTab("collections")}
            >
              Collections
            </StyledTabsTrigger>
            <StyledTabsTrigger
              value="claims"
              active={activeTab === "claims"}
              onClick={() => setActiveTab("claims")}
            >
              Claims
            </StyledTabsTrigger>
            <StyledTabsTrigger
              value="bonding-curve"
              active={activeTab === "bonding-curve"}
              onClick={() => setActiveTab("bonding-curve")}
            >
              Voronoi Bonding
            </StyledTabsTrigger>
          </StyledTabsList>

          {activeTab === "collections" && (
            <StyledTabsContent className="mt-0">
              <LandUnitsCollections />
            </StyledTabsContent>
          )}

          {activeTab === "claims" && (
            <StyledTabsContent className="mt-0">
              <LandUnitsClaims />
            </StyledTabsContent>
          )}

          {activeTab === "bonding-curve" && (
            <StyledTabsContent className="mt-0">
              <LandUnitsBondingCurve />
            </StyledTabsContent>
          )}
        </StyledTabs>
      </div>
    </section>
  );
}
