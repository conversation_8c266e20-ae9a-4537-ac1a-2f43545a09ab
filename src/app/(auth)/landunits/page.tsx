"use client";

import IbiIcon from "@/components/IbiUi/IbiIcon";
import { LandUnitsBondingCurve } from "@/components/LandUnits/BondingCurve";
import { LandUnitsClaims } from "@/components/LandUnits/Claims";
import { LandUnitsCollections } from "@/components/LandUnits/Collections";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import React from "react";

export default function LandUnits() {
  return (
    <section className="w-full h-screen overflow-y-auto max-[750px]:pb-20">
      <header
        id="dashboard-header"
        className="h-auto min-h-[108px] bg-gradient-to-b from-[#01050dF9] to-[#01050dD9] backdrop-blur-md py-6 sticky top-0 z-10 max-[750px]:pt-10 border-b border-slate-800/50 max-[1280px]:px-4"
      >
        <div className="h-full max-w-7xl mx-auto flex justify-between flex-col">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-slate-800/50 rounded-lg">
                <IbiIcon icon="tdesign:dashboard-1-filled" className="text-xl text-emerald-400" />
              </div>
              <h1 className="text-xl sm:text-2xl font-bold">Land Units</h1>
            </div>
            <Button size="sm" className="gap-2" href="/claim">
              <IbiIcon icon="arcticons:mint-browser" />
              <span>Browse map</span>
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl max-[1280px]:px-4 mx-auto mt-4 sm:mt-7">
        <Tabs defaultValue="collections" className="w-full">
          <TabsList className="bg-slate-800/50 mb-4">
            <TabsTrigger value="collections">Collections</TabsTrigger>
            <TabsTrigger value="claims">Claims</TabsTrigger>
            <TabsTrigger value="bonding-curve">Voronoi Bonding</TabsTrigger>
          </TabsList>

          <TabsContent value="collections" className="mt-0">
            <LandUnitsCollections />
          </TabsContent>

          <TabsContent value="claims" className="mt-0">
            <LandUnitsClaims />
          </TabsContent>

          <TabsContent value="bonding-curve" className="mt-0">
            <LandUnitsBondingCurve />
          </TabsContent>
        </Tabs>
      </div>
    </section>
  );
}
