"use client";

// import MapMenu from "@/components/IbiUi/IbiMap/MapMenu";
import { lazy, useState } from "react";

const MapContainer = lazy(() => import("@/components/MapContainer"));
const MapControllers = lazy(() => import("@/components/IbiUi/IbiMap/MapControllers"));

export default function World() {
  const [currentMapType] = useState<"default" | "biome" | "hydro">("default");

  return (
    <>
      {/* <div className="absolute top-4 left-[75px] z-10">
        <MapMenu currentMapType={currentMapType} setCurrentMapType={setCurrentMapType} />
      </div> */}

      <MapContainer currentMapType={currentMapType} />

      {currentMapType === "default" && <MapControllers />}
    </>
  );
}
