"use client";

import IbiIcon from "@/components/IbiUi/IbiIcon";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ABC_WHITE_PLUS_BOLD, ABC_WHITE_PLUS_LIGHT } from "@/utils/configs";
import { OrbitControls, useGLTF } from "@react-three/drei";
import { Canvas, useFrame } from "@react-three/fiber";
import React, { useRef } from "react";

function JungleTree3D() {
  const gltf = useGLTF("/3d/jungle_tree/scene.gltf", true);
  const modeloRef: any = useRef();

  useFrame(() => {
    if (modeloRef.current) {
      modeloRef.current.rotation.y += 0.001;
    }
  });

  return (
    <group ref={modeloRef} position={[0, -12.3, 0]}>
      <primitive object={gltf.scene} dispose={null} />
    </group>
  );
}

const Philosophy = () => {
  return (
    <div className="flex h-[calc(100vh-68px)]">
      <div className="w-full sm:w-[65%] h-full border-r border-[#ffffff4b] flex flex-col relative">
        <div className="sticky top-0 z-10 min-h-[178.77px] h-[178.77px] border-b border-[#ffffff4b] flex items-center px-6 sm:px-20 bg-primary-dark">
          <div className="flex items-center gap-8">
            <h1
              className="font-bold text-6xl max-[1880px]:text-5xl max-[1600px]:text-4xl animate__animated animate__fadeIn delay-0-2s"
              style={ABC_WHITE_PLUS_BOLD.style}
            >
              DIGITAL EMERGENCE (bio/acc)
            </h1>
          </div>
        </div>

        <ScrollArea className="flex-1">
          <div className="px-6 sm:px-20 py-10">
            <section className="space-y-4">
              <p style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-300 leading-relaxed font-normal">
                The only way to bring the real world into the digital realm is through the vessel of monetary value and
                scarcity. Digital space, by its very nature, tends toward frictionless replication: anything can be
                copied at virtually zero marginal cost, and so “value” in the conventional sense is hard to anchor.
                Nature, on the other hand, manifests itself in physical, thermodynamic processes—forests growing, oceans
                absorbing carbon, ecosystems maintaining balance—all of which are grounded in irreducible real-world
                constraints. To bridge these two spheres, one must find a way to embody nature’s constraints in a
                digital form that cannot be trivially duplicated or manipulated.
              </p>
              <p style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-300 leading-relaxed font-normal">
                The essence of nature in the digital realm is transposed through an economic grammar. Rather than trying
                to capture the total reality of a forest—its biodiversity, its aesthetics, its spiritual and ecological
                richness—the hyperstructure focuses on one universally recognized proxy for significance: *value. By
                anchoring currency issuance to the existence (or health, or coverage) of a forest, the forest becomes
                the “miner” in a trustless system, much like how the thermodynamic labor of computational Proof of Work
                underpins Bitcoin. The presence of nature itself is the source of the currency’s legitimacy: if the
                forest ceases to exist, the currency stops being minted. If the forest expands, issuance grows. Nature’s
                processes become the substrate of digital scarcity.
              </p>
              <p style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-300 leading-relaxed font-normal">
                By tying monetary creation to forest coverage—verified by satellite imagery—the inflationary aspect
                depends on a living, thermodynamically bound phenomenon. Forests draw carbon from the atmosphere,
                produce oxygen, and perform countless ecological services. This ecological “work” might be
                philosophically recast as “nature’s labor,” paralleling certain labor theories of value but expanding
                them to include non-human processes. As long as nature endures (or thrives), new currency flows into
                circulation, reinforcing the link between digital scarcity and earthly reality.
              </p>
              <p style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-300 leading-relaxed font-normal">
                Each new coin minted stands for an actual energy or material process occurring in the real world—trees
                growing and metabolizing, photosynthesis turning sunlight into chemical energy, carbon dioxide into
                cellulose. Nothing in the physical domain comes without cost: energy is always converted from one form
                to another, with inevitable losses. In a purely digital setting—one absent of computational Proof of
                Work or another real-world anchor—there is no embedded cost, no friction, and hence no intrinsic
                boundary to replication. By “mirroring” forest existence, the currency harnesses the natural energy flow
                that underlies all ecosystems, letting thermodynamic constraints shape its digital supply.
              </p>
              <p style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-300 leading-relaxed font-normal">
                Such a system, implemented as an unstoppable hyperstructure—a decentralized, self-executing smart
                contract— means it cannot easily be turned off or corrupted by a single authority. In that sense, it
                becomes an autonomous agent representing nature’s processes in cyberspace, forging a new interface
                between planetary biology and human economic exchange. This unstoppable quality is vital: if the
                contract could be shut down, the digital reflection of nature’s continuous work would lose its
                permanence and trustworthiness. Instead, by running on a public, resilient blockchain, the
                hyperstructure ensures that as long as satellites observe forests, and as long as the oracle feeds this
                data on-chain, the currency will continue to be minted independently of any centralized will.
              </p>
              <p style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-300 leading-relaxed font-normal">
                Some would argue that “nature as value” is a pragmatic step toward widespread ecological preservation,
                because it aligns self-interest (collecting currency) with planetary well-being (maintaining forests).
                Others may see it as an overreach of economic rationality, reducing the ineffable complexity and worth
                of living ecosystems to a stream of minted coins. Either way, the unstoppable hyperstructure sets in
                motion a test: Can harnessing financial incentives at this scale truly safeguard the ecosystems we rely
                on?
              </p>
              <p style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-300 leading-relaxed font-normal">
                Purely symbolic worlds (software, networks, virtual interactions) can be stabilized and made meaningful
                only if they latch onto something irreducibly scarce and valuable “beyond the wire.” Bitcoin did this
                with computational work—heat, electricity, and time. A forest-backed system substitutes CPU cycles for
                the natural processes of photosynthesis, growth, and biodiversity; an attempt to make the planet itself
                a co-participant in the generation of digital wealth, thereby emerging nature into the digital space via
                the only language that consistently represents its scarcity and thermodynamic contribution: a type of
                currency.
              </p>
              {/* <p style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-300 leading-relaxed font-normal">
                This thermodynamic principle extends beyond biological matter. Whether we examine molecules, organisms,
                or civilizations, the same physics of stochastic processes applies: paths that dissipate more heat are
                exponentially more likely than their reverse. The ratio of path probabilities is proportional to the
                exponential difference in heat dissipated.
              </p>
              <p style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-300 leading-relaxed font-normal">
                Intelligence emerges as a specialized adaptation that minimizes &ldquo;expected future surprisal&ldquo;
                - it creates predictive models that reduce uncertainty about where to find free energy. As Jeremy
                England&apos;s work suggests, intelligence is not separate from thermodynamics but emerges from it. The
                more effectively a system can predict and control its environment, the better it can capture and
                dissipate energy.
              </p>
              <p style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-300 leading-relaxed font-normal">
                Capital represents a higher-order emergence from this process. While money serves as a medium of
                exchange, capital is an organizing system that enables meta-organisms (corporations, markets,
                civilizations) to coordinate energy extraction and dissipation at massive scales. The
                &ldquo;techno-capital-memetic machine&ldquo; becomes a form of distributed intelligence, where markets
                post-select for entities better at capturing and deploying energy/capital for growth. This creates a
                multi-scale feedback loop:
              </p>
              <ul>
                <li style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-400 leading-relaxed font-normal">
                  - Memes shape attention
                </li>
                <li style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-400 leading-relaxed font-normal">
                  - Attention directs capital flows
                </li>
                <li style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-400 leading-relaxed font-normal">
                  - Capital enables technological development
                </li>
                <li style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-400 leading-relaxed font-normal">
                  - Technology reshapes human behavior and capabilities
                </li>
                <li style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-400 leading-relaxed font-normal">
                  - New behaviors generate new memes
                </li>
              </ul>
              <p style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-300 leading-relaxed font-normal">
                The Kardashev scale provides a metric for this process - measuring civilization&apos;s total energy
                capture and dissipation capability. Intelligence allows us to extract more value from each unit of
                energy, but ultimately drives us toward capturing more total energy. Any subsystem that fails to align
                with this growth imperative gets selected out.
              </p>
              <p style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-300 leading-relaxed font-normal">
                This process cannot be centrally planned or controlled. The world is too chaotic and complex. Instead,
                we need decentralized, hierarchical systems of cybernetic control loops - like markets and open source
                collaboration networks. These enable parallel exploration of possibility space while maintaining
                fault-tolerant coordination.
              </p>
              <p style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-300 leading-relaxed font-normal">
                The digital realm and blockchain technology allow us to manifest these principles programmatically,
                creating new forms of capital and coordination that can better align with and accelerate these
                fundamental thermodynamic drives. They provide the infrastructure for a new kind of civilization-scale
                intelligence.
              </p>
              <p style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-300 leading-relaxed font-normal">
                <strong>This is bio/acc:</strong> understanding and accelerating these processes by designing better
                systems for coordination, exploration, and growth - always aligned with the fundamental thermodynamic
                arrow toward greater energy dissipation and complexity.
              </p> */}
              <div className="flex gap-1 !mt-14">
                <p style={ABC_WHITE_PLUS_LIGHT.style} className="text-gray-300 leading-relaxed font-semibold">
                  Written by humans.
                </p>
                <IbiIcon icon="mdi:sign" />
              </div>
            </section>
          </div>
        </ScrollArea>
      </div>

      <div className="flex-1 h-full relative overflow-hidden hidden sm:block">
        <Canvas camera={{ position: [20, 10, 20], fov: 48, near: 0.1, far: 1000 }}>
          <ambientLight />
          <pointLight position={[10, 10, 10]} />
          <JungleTree3D />
          <OrbitControls
            maxPolarAngle={Math.PI / 2}
            minPolarAngle={Math.PI / 2}
            dampingFactor={0.05}
            enableDamping={true}
            rotateSpeed={0.01}
            enableZoom={false}
          />
        </Canvas>
      </div>
    </div>
  );
};

export default Philosophy;
