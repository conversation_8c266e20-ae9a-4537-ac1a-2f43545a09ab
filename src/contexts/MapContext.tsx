import { CurrentBaseMap, CurrentLayers, MapContextProps } from "@/types";
import { useMemo, useRef, useState } from "react";
import { MapRef } from "react-map-gl";
import { createContext } from "use-context-selector";

const initialValue: MapContextProps = {
  mapRef: null,
  minimapRef: null,
  mapLoaded: false,
  currentLayers: [],
  currentBaseMap: "default",
  showProperties: true,
  showRestorations: true,
  setShowProperties: () => {},
  setShowRestorations: () => {},
  setCurrentLayers: () => {},
  setCurrentBaseMap: () => {},
  setMapLoaded: () => {},
};

export const MapContext = createContext<MapContextProps>(initialValue);

export function MapProvider({ children }: { children: React.ReactNode }) {
  const mapRef = useRef<mapboxgl.Map | null>(null);
  const minimapRef = useRef<MapRef | null>(null);
  const [showProperties, setShowProperties] = useState(true);
  const [showRestorations, setShowRestorations] = useState(false);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [currentLayers, setCurrentLayers] = useState<CurrentLayers[]>([]);
  const [currentBaseMap, setCurrentBaseMap] = useState<CurrentBaseMap>("default");

  const value = useMemo(
    () => ({
      mapRef,
      minimapRef,
      currentLayers,
      currentBaseMap,
      mapLoaded,
      showProperties,
      showRestorations,
      setShowProperties,
      setShowRestorations,
      setCurrentLayers,
      setCurrentBaseMap,
      setMapLoaded,
    }),
    [
      mapRef,
      minimapRef,
      currentLayers,
      currentBaseMap,
      mapLoaded,
      showProperties,
      showRestorations,
      setShowProperties,
      setShowRestorations,
      setCurrentLayers,
      setCurrentBaseMap,
      setMapLoaded,
    ],
  );

  return <MapContext.Provider value={value}>{children}</MapContext.Provider>;
}
