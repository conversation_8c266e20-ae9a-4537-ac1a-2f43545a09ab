import { BASE_URL } from "./constants";

type HttpResponse<T> = {
  error: Error | null;
  result: Partial<T>;
};

/**
 * Makes a request to the given path.
 * @param path the path to make the request to
 * @param options the options to pass to the fetch function
 * @param fullUrl the full URL to make the request to, if not given, will use the BASE_URL constant
 * @returns a HttpResponse with the result and error
 */
export async function makeRequest<T>(
  options: RequestInit = {},
  path?: string,
  fullUrl?: string,
  contentType?: string,
): Promise<HttpResponse<T>> {
  const mergedOptions: RequestInit = {
    ...options,
    headers: {
      ...options.headers,
      "Content-Type": contentType ?? "application/json",
    },
  };

  try {
    const response = await fetch(fullUrl ?? `${BASE_URL}${path}`, mergedOptions);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: T = await response.json();
    return { error: null, result };
  } catch (error) {
    return { error: error as Error, result: {} };
  }
}
