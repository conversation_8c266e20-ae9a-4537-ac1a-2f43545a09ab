export function shortenText(text: string, maxLength: number): string {
  if (!text) return "";

  if (text?.length <= maxLength) {
    return text;
  }

  const charsToShow = Math.floor((maxLength - 3) / 2);
  const start = text.substring(0, charsToShow);
  const end = text.substring(text.length - charsToShow);

  return `${start}...${end}`;
}

export const compactName = (str: string) => str?.toLowerCase()?.replace(/\s+/g, "");
