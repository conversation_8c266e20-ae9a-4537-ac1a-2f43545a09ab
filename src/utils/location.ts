import { Locations } from "@/types";

import { makeRequest } from "./make-request";

/**
 * Returns the current user location as a { lat: number, lng: number } object.
 * @returns {Promise<{ lat: number; lng: number } | undefined>}
 */
export async function getCurrentLatLng(): Promise<{ lat: number; lng: number } | undefined> {
  if (!navigator.geolocation) {
    console.warn("Geolocation is not supported by this browser");
    return undefined;
  }

  try {
    const position = await new Promise<GeolocationPosition>((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(resolve, reject, {
        timeout: 10000,
        maximumAge: 0,
        enableHighAccuracy: true,
      });
    });

    return {
      lat: position.coords.latitude,
      lng: position.coords.longitude,
    };
  } catch (err) {
    console.warn("Error getting location:", err);
    return undefined;
  }
}

/**
 * Returns the current user's address information
 * @returns {Promise<Partial<Locations> | undefined>}
 */
export async function getCurrentAddress(): Promise<Partial<Locations> | undefined> {
  try {
    const location = await getCurrentLatLng();

    // Return early if no location is available
    if (!location || typeof location.lat !== "number" || typeof location.lng !== "number") {
      console.warn("Location data is unavailable or invalid");
      return undefined;
    }

    const { result, error } = await makeRequest<Partial<Locations>>(
      {
        method: "GET",
      },
      undefined,
      `https://api.positionstack.com/v1/reverse?access_key=********************************&query=${location.lat},${location.lng}`,
    );

    if (error) {
      console.error("Error fetching address:", error);
      return undefined;
    }

    if (!result) {
      console.warn("No address data received");
      return undefined;
    }

    return result;
  } catch (error) {
    console.error("Error in getCurrentAddress:", error);
    return undefined;
  }
}

/**
 * Returns a weather icon based on weather conditions
 * @param {Values} values - Weather condition values
 * @returns {string} Icon identifier
 */
export function getWeatherIcon(): string {
  // if (!values) {
  //   return "default-weather-icon";
  // }

  // const {
  //   weatherCode = 0,
  //   precipitationProbability = 0,
  //   cloudCover = 0,
  //   snowIntensity = 0,
  //   rainIntensity = 0,
  //   temperature = 0,
  // } = values;

  // switch (true) {
  //   case weatherCode === 1000:
  //     return "fluent:weather-sunny-16-regular";
  //   case weatherCode >= 1001 && weatherCode <= 1100:
  //     return "carbon:cloudy";
  //   case rainIntensity > 0 || precipitationProbability > 50:
  //     return "ion:rainy-outline";
  //   case snowIntensity > 0:
  //     return "meteocons:wind-snow-fill";
  //   case cloudCover > 80:
  //     return "fluent:weather-cloudy-20-filled";
  //   case temperature < 0:
  //     return "fluent-mdl2:freezing";
  //   default:
  //     return "default-weather-icon";
  // }

  return "default-weather-icon";
}
