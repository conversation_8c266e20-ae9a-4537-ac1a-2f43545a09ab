// import InviteCodeCard from "@/components/InviteCodeCard";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { DialogProps } from "@radix-ui/react-dialog";
import { lazy } from "react";

const SubmitCodeCard = lazy(() => import("../SubmitCodeCard"));

const SubmitCodeModal = ({
  open,
  onOpenChange,
  dialogProps,
}: {
  title: string;
  description: string;
  open: boolean;
  onOpenChange?: (open: boolean) => void;
  dialogProps?: DialogProps;
}) => {
  return (
    <Dialog {...dialogProps} open={open} onOpenChange={onOpenChange}>
      <DialogContent hideClose className="bg-[#000000cc]">
        <DialogHeader>
          <DialogTitle className="text-center font-semibold text-2xl">Insert your invite InviteCodeCard</DialogTitle>
          <DialogDescription className="text-center opacity-70 text-sm">
            Please insert your invite code to join the community.
          </DialogDescription>
        </DialogHeader>
        <SubmitCodeCard />
      </DialogContent>
    </Dialog>
  );
};

export default SubmitCodeModal;
