import { InputOTP, InputOTPGroup, InputOTPSeparator, InputOTPSlot } from "@/components/ui/input-otp";
import useCode from "@/hooks/useCode";
import usePrivyAuth from "@/hooks/usePrivyAuth";
import { ABC_WHITE_PLUS_BOLD, ABC_WHITE_PLUS_LIGHT } from "@/utils/configs";
import { handleStorage } from "@/utils/storage";
import { REGEXP_ONLY_DIGITS_AND_CHARS } from "input-otp";
import { useEffect } from "react";

/**
 * Component for entering and submitting invite codes
 */
const SubmitCodeCard = () => {
  const { code, handleOTPChange, isValidating, failed } = useCode();
  const { onLogin } = usePrivyAuth();

  /**
   * Handles the submission of the invite code
   * Stores the code in localStorage and triggers the login process
   */
  const handleCodeSubmit = async () => {
    if (code.length === 6) {
      handleStorage("local", "inviteCode", "create", code);
      onLogin();
    }
  };

  // Automatically submit the code when all 6 characters are entered
  useEffect(() => {
    if (code.length === 6) {
      handleCodeSubmit();
    }
  }, [code]);

  return (
    <div className="card-sm bg-[#ffffff05] rounded-lg p-4 sm:p-6 backdrop-blur-sm border border-[#ffffff1a] w-[600px]">
      <h1 style={ABC_WHITE_PLUS_BOLD.style} className="text-xl sm:text-2xl mb-2 text-center">
        Enter your invite code
      </h1>
      <p style={ABC_WHITE_PLUS_LIGHT.style} className="text-xs sm:text-sm text-center mb-6 sm:mb-8 text-[#ffffff80]">
        Use your 6-digit code to access the platform
      </p>

      <div className="flex flex-col items-center">
        <InputOTP
          maxLength={6}
          pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
          onChange={handleOTPChange}
          inputMode="text"
          className="gap-1"
          disabled={isValidating}
        >
          <InputOTPGroup className="space-x-5">
            {[0, 1, 2].map((index) => (
              <InputOTPSlot
                key={index}
                className={`rounded-md border ${
                  failed ? "border-red-500" : "border-[#ffffff1a]"
                } bg-[#ffffff0a] w-7 h-7 sm:w-10 sm:h-10 text-center text-sm sm:text-base focus:border-[#ffffff40] transition-colors`}
                index={index}
              />
            ))}
          </InputOTPGroup>

          <InputOTPSeparator>
            <div className="w-2 h-px bg-[#ffffff1a]" />
          </InputOTPSeparator>

          <InputOTPGroup className="space-x-5">
            {[3, 4, 5].map((index) => (
              <InputOTPSlot
                key={index}
                className={`rounded-md border ${
                  failed ? "border-red-500" : "border-[#ffffff1a]"
                } bg-[#ffffff0a] w-7 h-7 sm:w-10 sm:h-10 text-center text-sm sm:text-base focus:border-[#ffffff40] transition-colors`}
                index={index}
              />
            ))}
          </InputOTPGroup>
        </InputOTP>

        {isValidating && <p className="text-sm text-blue-400 mt-4">Validating your invite code...</p>}

        {failed && <p className="text-sm text-red-500 mt-4">Invalid invite code. Please try again.</p>}
      </div>
    </div>
  );
};

export default SubmitCodeCard;
