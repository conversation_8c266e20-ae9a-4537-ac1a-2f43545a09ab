import { BellIcon } from "@radix-ui/react-icons";
import React, { useState } from "react";

import { Sheet, Sheet<PERSON>ontent, She<PERSON><PERSON>eader, SheetTitle, SheetTrigger } from "../ui/sheet";

const Notifications = () => {
  const [open, setOpen] = useState(false);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <button className="rounded-full p-2 hover:bg-neutral-800 dark:hover:bg-neutral-800">
          <BellIcon className="h-5 w-5" />
        </button>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>Notifications</SheetTitle>
        </SheetHeader>
        <div className="space-y-4">
          {/* Notification content will go here */}
          <div>Notifications</div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default Notifications;
