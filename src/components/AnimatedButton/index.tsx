"use client";

import { useGSAP } from "@gsap/react";
import gsap from "gsap";
import React, { useEffect, useRef, useState } from "react";

gsap.registerPlugin(useGSAP);

interface AnimatedButtonProps {
  text?: string;
  onClick?: () => void;
  className?: string;
  width?: number;
  height?: number;
}

export const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  text = "LOGIN",
  onClick,
  className = "",
  width = 195,
  height = 74,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const animationRef = useRef<number>(0);
  const [isHovering, setIsHovering] = useState(false);
  const hoverAnimRef = useRef({ progress: 0 });
  const [fontLoaded, setFontLoaded] = useState(false);

  const scrambleChars = "!@#$%^&*()_+=<>?/[]{}ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";

  useEffect(() => {
    const testFont = new FontFace("denim", 'local("denim"), local("Denim")');

    testFont
      .load()
      .then(() => setFontLoaded(true))
      .catch(() => setFontLoaded(true));
  }, []);

  useGSAP(
    () => {
      if (!fontLoaded) return;

      const canvas = canvasRef.current;
      if (!canvas) return;

      const ctx = canvas.getContext("2d");
      if (!ctx) return;

      const columns = Math.floor((canvas.width - 20) / 15);
      const rows = Math.floor((canvas.height - 20) / 15);

      const drawButton = () => {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        ctx.fillStyle = "#A5FF2B";
        ctx.beginPath();
        ctx.moveTo(10, 0);
        ctx.lineTo(width, 0);
        ctx.lineTo(width, height - 10);
        ctx.lineTo(width - 10, height);
        ctx.lineTo(0, height);
        ctx.lineTo(0, 10);
        ctx.closePath();
        ctx.fill();

        for (let x = 0; x <= columns; x++) {
          for (let y = 0; y <= rows; y++) {
            const dotX = 10 + x * 15;
            const dotY = 10 + y * 15;
            if (dotX >= width - 10 || dotY >= height - 10) continue;

            const currentCol = Math.floor(hoverAnimRef.current.progress * columns);
            const intensity = isHovering && (x === currentCol || x === currentCol + 1) ? 1.1 : 0.8;

            ctx.fillStyle = `rgba(93, 128, 0, ${0.5 + 0.2 * intensity})`;
            ctx.beginPath();
            ctx.arc(dotX, dotY, 1.2 * intensity, 0, Math.PI * 2);
            ctx.fill();
          }
        }

        ctx.font = "bold 14px denim, monospace";
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";

        const spacing = 12; // Ajuste o valor para controlar espaçamento entre letras
        const totalWidth = (text.length - 1) * spacing;
        const baseX = canvas.width / 2 - totalWidth / 2;
        const baseY = canvas.height / 2;

        for (let i = 0; i < text.length; i++) {
          let char = text[i];
          const delay = i * 0.08;

          if (isHovering && hoverAnimRef.current.progress > delay && hoverAnimRef.current.progress < delay + 0.5) {
            char = scrambleChars[Math.floor(Math.random() * scrambleChars.length)];
          }

          ctx.fillStyle = `rgba(0, 0, 0, 1)`;
          const charX = baseX + i * spacing;
          ctx.fillText(char, charX, baseY);
        }
      };

      const animate = () => {
        drawButton();
        animationRef.current = requestAnimationFrame(animate);
      };

      animate();

      return () => cancelAnimationFrame(animationRef.current);
    },
    { dependencies: [text, isHovering, width, height, fontLoaded] },
  );

  const { contextSafe } = useGSAP({ scope: buttonRef });

  const handleMouseEnter = contextSafe(() => {
    setIsHovering(true);
    hoverAnimRef.current.progress = 0;
    gsap.to(hoverAnimRef.current, {
      progress: 1,
      duration: 0.6,
      ease: "power1.out",
      onComplete: () => setIsHovering(false),
    });
  });

  return (
    <button
      ref={buttonRef}
      className={`p-0 bg-transparent border-none cursor-pointer relative ${className}`}
      style={{ width, height }}
      onClick={onClick}
      onMouseEnter={handleMouseEnter}
    >
      <canvas ref={canvasRef} width={width} height={height} className="block" />
    </button>
  );
};
