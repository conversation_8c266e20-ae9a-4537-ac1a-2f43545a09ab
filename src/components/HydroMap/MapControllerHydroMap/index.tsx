import IbiIcon from "@/components/IbiUi/IbiIcon";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useHydroshedsManagementStore } from "@/hooks/useLayerControl/useHydrosheds/store";
import { cn } from "@/lib/utils";
import { useMapSelector } from "@/stores/map.store";
import React from "react";

const MapControllerHydroMap = () => {
  const { currentHydroshedLevel, setCurrentHydroshedLevel, setSelectedHydroshedLevel } = useHydroshedsManagementStore();
  const triggerPropertiesReload = useMapSelector.use.triggerPropertiesReload();
  const triggerHydroshedsReload = useMapSelector.use.triggerHydroshedsReload();

  const hydroshedsLevelOptions = [
    { id: "hybas_7", label: "Hybas 7" },
    { id: "hybas_8", label: "Hybas 8" },
    { id: "hybas_9", label: "Hybas 9" },
    { id: "hybas_10", label: "Hybas 10" },
    { id: "hybas_11", label: "Hybas 11" },
    { id: "hybas_12", label: "Hybas 12 (default)" },
  ];

  return (
    <div className="absolute -top-[85px] sm:-top-[75px] left-0 mt-[140px] ml-3.5 sm:ml-[78px] flex flex-col gap-2">
      <Popover>
        <PopoverTrigger asChild>
          <button className="border border-[#ffffff56] hover:border-[#ffffff] hover:bg-[#ffffff56] overflow-hidden rounded-md h-[40px] w-fit bg-primary-dark flex items-center justify-center px-2 gap-2">
            <IbiIcon icon="mdi:layers" className="text-xl" />
            Hydrosheds Levels
          </button>
        </PopoverTrigger>
        <PopoverContent
          sideOffset={12}
          side="right"
          align="start"
          className="w-[298px] rounded-none border-none p-[1px] bg-[#ffffff2f] layers-box-clip-path relative text-white"
        >
          <div className="layers-box-clip-path bg-[#0A0E15] size-full px-4 pb-6 pt-5">
            <h1 className="text-gray-400 text-[12px]">Hydrosheds Levels</h1>
            <div className="mt-3 flex flex-col gap-2">
              {hydroshedsLevelOptions.map((option) => (
                <div
                  key={option.id}
                  className={cn(
                    "p-2 rounded cursor-pointer hover:bg-[#ffffff15]",
                    currentHydroshedLevel === option.label && "bg-[#ffffff2f]",
                  )}
                  onClick={() => {
                    setCurrentHydroshedLevel(option.label);
                    setSelectedHydroshedLevel(option);
                    setTimeout(() => {
                      triggerPropertiesReload();
                      triggerHydroshedsReload();
                    }, 300);
                  }}
                >
                  <h2 className="text-gray-300 text-[14px]">{option.label}</h2>
                </div>
              ))}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default MapControllerHydroMap;
