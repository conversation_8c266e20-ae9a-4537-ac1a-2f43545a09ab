import { useBaseMap } from "@/hooks/useBaseMap";
import { useDetectLocation } from "@/hooks/useDetectLocation";
import { useHydrosheds } from "@/hooks/useLayerControl/useHydrosheds";
import { useHydroshedsManagementStore } from "@/hooks/useLayerControl/useHydrosheds/store";
import useMap from "@/hooks/useMap";
import { useMapSelector } from "@/stores/map.store";
import { MAPBOX_TOKEN, MAP_PROJECTION } from "@/utils/constants";
import "mapbox-gl/dist/mapbox-gl.css";
import { useCallback, useEffect, useRef, useState } from "react";
import { Map } from "react-map-gl";

import MapFooterToolbar from "../MapFooterToolbar";

// import MapControllerHydroMap from "./MapControllerHydroMap";

const HydroMap = () => {
  const mapRef = useRef<mapboxgl.Map | null>(null);
  const { setMapLoaded } = useMap();
  const { mapStyle } = useBaseMap();
  const padding = useMapSelector.use.padding();
  const { latitude, longitude } = useDetectLocation();
  const triggerHydroshedsReload = useMapSelector.use.triggerHydroshedsReload();
  const { setCurrentHydroshedLevel, setSelectedHydroshedLevel } = useHydroshedsManagementStore();

  const [mapLoaded, setLocalMapLoaded] = useState(false);
  const [mapInitialized, setMapInitialized] = useState(false);
  const [hydroshedsInitialized, setHydroshedsInitialized] = useState(false);

  const { clearSelection, setupHydroshedsLayer } = useHydrosheds(mapRef, mapLoaded);

  const defaultViewState = {
    latitude: -14.72,
    longitude: -51.419,
    zoom: 8,
  };

  const [viewState, setViewState] = useState(defaultViewState);

  const clickHandlerRef = useRef<((e: any) => void) | null>(null);

  useEffect(() => {
    setSelectedHydroshedLevel({
      id: "hybas_12",
      label: "Hybas 12",
    });
    setCurrentHydroshedLevel("Hybas 12 (default)");
  }, []);

  useEffect(() => {
    if (mapRef.current && mapLoaded) {
      setMapLoaded(true);
      (window as any).__hydroMapInstance = mapRef.current;
    }
  }, [mapRef, mapLoaded, setMapLoaded]);

  useEffect(() => {
    if (mapLoaded && mapRef.current && !hydroshedsInitialized) {
      clearSelection();

      setupHydroshedsLayer();

      setTimeout(() => {
        triggerHydroshedsReload();
      }, 500);

      setHydroshedsInitialized(true);
    }
  }, [mapLoaded, setupHydroshedsLayer, triggerHydroshedsReload, clearSelection, hydroshedsInitialized]);

  useEffect(() => {
    if (latitude && longitude && !mapInitialized) {
      setViewState({
        latitude,
        longitude,
        zoom: 8,
      });
      setMapInitialized(true);

      if (mapRef.current && mapLoaded) {
        mapRef.current.flyTo({
          center: [longitude, latitude],
          zoom: 8,
          essential: true,
          duration: 1000,
        });
      }
    }
  }, [latitude, longitude, mapInitialized, mapLoaded]);

  useEffect(() => {
    return () => {
      if (mapRef.current && clickHandlerRef.current) {
        mapRef.current.off("click", clickHandlerRef.current);
      }
    };
  }, []);

  const handleMapLoad = useCallback(
    (event: any) => {
      mapRef.current = event.target;
      setLocalMapLoaded(true);
      setMapLoaded(true);

      if (latitude && longitude && mapRef.current) {
        mapRef.current.flyTo({
          center: [longitude, latitude],
          zoom: 8,
          essential: true,
          duration: 1000,
        });
      }

      const map = event.target;
      if (!map.getSource("mapbox-dem")) {
        map.addSource("mapbox-dem", {
          type: "raster-dem",
          url: "mapbox://mapbox.mapbox-terrain-dem-v1",
          tileSize: 512,
          maxzoom: 22,
        });
      }

      const clickHandler = (e: any) => {
        const hydroshedsFeatures = map.queryRenderedFeatures(e.point, {
          layers: ["hydrosheds_fill"],
        });

        if (hydroshedsFeatures.length === 0) {
          clearSelection();
        }
      };

      clickHandlerRef.current = clickHandler;

      map.on("click", clickHandler);
    },
    [latitude, longitude, setMapLoaded, clearSelection],
  );

  return (
    <div className="w-full h-full flex flex-col relative">
      <Map
        ref={mapRef as any}
        mapStyle={mapStyle}
        initialViewState={viewState}
        padding={padding}
        mapboxAccessToken={MAPBOX_TOKEN}
        projection={MAP_PROJECTION}
        onLoad={handleMapLoad}
        interactiveLayerIds={["hydrosheds"]}
        style={{ width: "100%", height: "100%" }}
        renderWorldCopies={false}
        attributionControl={false}
        preserveDrawingBuffer={false}
        antialias={false}
        terrain={{ source: "mapbox-dem", exaggeration: 2 }}
        pitchWithRotate={true}
      />
      {/* <MapControllerHydroMap /> */}
      <div
        className="absolute top-0 left-0 w-full h-full pointer-events-none"
        style={{
          background:
            "linear-gradient(to bottom, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0.1) 15%, rgba(0,0,0,0.1) 85%, rgba(0,0,0,0.9) 100%)",
          zIndex: 10,
        }}
      />
      <MapFooterToolbar className="absolute bottom-0 left-0 right-0 z-20" />
    </div>
  );
};

export default HydroMap;
