import { cn } from "@/lib/utils";
import { IbiCardProps } from "@/types";
import React from "react";

import IbiIcon from "../IbiIcon";

const IbiCard = ({
  children,
  pathVariant,
  className,
  headerTitle,
  headerSubtitle,
  headerIcon,
  ...props
}: IbiCardProps) => {
  return (
    <div
      className={cn(
        "font-extrabold m-6 flex justify-center items-center bg-neutral-transparent p-[2px]",
        pathVariant,
        className,
      )}
    >
      <div className={cn("w-full h-full bg-secondary-transparent p-6 flex flex-col", pathVariant)}>
        <div>
          <div className="flex justify-between [&>*]:text-neutral-8 [&>*]:text-[18px]">
            <div className="flex items-center gap-1.5">
              {headerIcon && <IbiIcon icon={headerIcon} className="text-neutral-8" />}
              <h1>{headerTitle}</h1>
            </div>
            {headerSubtitle && <h2>{headerSubtitle}</h2>}
          </div>
          <hr className="w-full bg-gradient-to-r from-neutral-4 to-neutral-5 h-[1px] border-none my-2" />
          <div className="flex justify-between text-[14px] font-[700] text-blank-2">
            {props.subHeaderTitle && <h3>{props.subHeaderTitle}</h3>}
            {props.subHeaderSubtitle && <h4>{props.subHeaderSubtitle}</h4>}
          </div>
          <div className="flex justify-between text-[16px] font-[700] text-blank-1">
            {props.subHeaderContent && <h3>{props.subHeaderContent}</h3>}
            {props.subHeaderSubtitleContent && <h4>{props.subHeaderSubtitleContent}</h4>}
          </div>
        </div>
        <div className="flex-1">{children}</div>
      </div>
    </div>
  );
};

export default IbiCard;
