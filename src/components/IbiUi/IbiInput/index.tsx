import { cn } from "@/lib/utils";
import { LabelProps } from "@radix-ui/react-label";
import React from "react";

import { Input, InputProps } from "../../ui/input";
import { Label } from "../../ui/label";
import IbiIcon from "../IbiIcon";

interface IbiInputProps extends InputProps {
  icon?: string;
  iconPosition?: "left" | "right";
  id: string;
  label?: string;
  className?: string;
  wrapperClass?: string;
  labelProps?: LabelProps;
}

const IbiInput = ({
  id,
  icon,
  iconPosition = "left",
  label,
  className,
  wrapperClass,
  labelProps,
  ...props
}: IbiInputProps) => {
  return (
    <div
      className={cn(
        `font-denim relative w-full gap-1.5 flex items-center ${iconPosition === "right" ? "flex-row-reverse" : ""}`,
        wrapperClass,
      )}
    >
      {icon && <IbiIcon icon={icon} className="text-white text-lg" />}
      <Label {...labelProps} htmlFor={id} className="flex-1 -mt-1">
        <span>{label}</span>
        <Input
          autoComplete="off"
          spellCheck="false"
          autoCorrect="off"
          {...props}
          id={id}
          className={cn("p-0 mt-1 placeholder:text-white/70", className)}
        />
      </Label>
    </div>
  );
};

export default IbiInput;
