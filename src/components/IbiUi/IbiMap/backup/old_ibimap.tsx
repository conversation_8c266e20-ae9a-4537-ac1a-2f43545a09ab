// //@ts-nocheck

// import LoadingSpinner from "@/components/LoadingSpinner";
// import { useBaseMap } from "@/hooks/useBaseMap";
// import { useDetectCountry } from "@/hooks/useDetectCountry";
// import { useForestHistory } from "@/hooks/useForestHistory";
// import useMap from "@/hooks/useMap";
// import { useMapLayers } from "@/hooks/useMapLayers";
// import { useMapZoom } from "@/hooks/useMapZoom";
// import { useOpenWeatherLayers } from "@/hooks/useLayerControl/useOpenWeatherLayers";
// import { usePropertyManagement } from "@/hooks/usePropertyManagement";
// import { useWeatherLayer } from "@/hooks/useLayerControl/useWeatherLayer";
// import { ExtendedMap, useWorldBoundaries } from "@/hooks/useLayerControl/useWorldBoundaries";
// import { territoriesService } from "@/services/territories.service";
// import { useMapSelector } from "@/stores/map.store";
// import {
//   INITIAL_COUNTRY_ZOOM,
//   MAPBOX_TOKEN,
//   MAP_PROJECTION,
//   PROPERTIES_MAX_ZOOM,
//   PROPERTIES_MIN_ZOOM,
//   propertyOutlineLayer,
// } from "@/utils/constants";
// import { handleStorage } from "@/utils/storage";
// import "mapbox-gl/dist/mapbox-gl.css";
// import { useCallback, useEffect, useState } from "react";
// import Map, { Layer, MapLayerMouseEvent, Source } from "react-map-gl";

// import MovebankLayer from "./MovebankLayer";

// const simulateClickOnLocation = (map: mapboxgl.Map, coordinates: { lng: number; lat: number }) => {
//   const point = map.project(coordinates);
//   const event = new MouseEvent("click", {
//     bubbles: true,
//     cancelable: true,
//     clientX: point.x,
//     clientY: point.y,
//   });
//   map.getCanvas().dispatchEvent(event);

//   const features = map.queryRenderedFeatures(point, { layers: ["properties"] });
//   return features.length > 0;
// };

// const IbiMap = () => {
//   const [propertyId, setPropertyId] = useState<string | null>(null);

//   const { mapRef, currentLayers, showProperties, showRestorations } = useMap();
//   const padding = useMapSelector.use.padding();

//   const {
//     WindLayer,
//     PrecipitationLayer,
//     CloudsLayer,
//     TemperatureLayer,
//     PressureLayer,
//     isLoading: weatherLoading,
//     error: weatherError,
//   } = useOpenWeatherLayers();

//   const checkLayersLoaded = (map: mapboxgl.Map): Promise<boolean> => {
//     return new Promise((resolve) => {
//       const checkLayers = () => {
//         const propertiesSource = map.getSource("properties-source");
//         if (!propertiesSource) {
//           setTimeout(checkLayers, 100);
//           return;
//         }

//         const propertiesLayer = map.getLayer("properties");
//         if (!propertiesLayer) {
//           setTimeout(checkLayers, 100);
//           return;
//         }

//         if ((propertiesSource as mapboxgl.GeoJSONSource).loaded()) {
//           resolve(true);
//         } else {
//           setTimeout(checkLayers, 100);
//         }
//       };

//       checkLayers();
//     });
//   };

//   const handleShareNavigation = useCallback(async () => {
//     const ibicode = handleStorage<string>("session", "ibiCode", "get");
//     if (!ibicode || !mapRef?.current) return;

//     try {
//       const map = (mapRef?.current as any).getMap();

//       map.dragPan.disable();
//       map.scrollZoom.disable();
//       map.doubleClickZoom.disable();
//       map.touchZoomRotate.disable();
//       map.getCanvas().style.cursor = "not-allowed";

//       const customCoordinates = await territoriesService.getCoordinates(ibicode);

//       map.flyTo({
//         center: [customCoordinates.data.coordinates[0], customCoordinates.data.coordinates[1]],
//         zoom: 12,
//         duration: 600,
//       });

//       await checkLayersLoaded(map);
//       await new Promise((resolve) => setTimeout(resolve, 2000));
//       simulateClickOnLocation(map, {
//         lng: customCoordinates.data.coordinates[0],
//         lat: customCoordinates.data.coordinates[1],
//       });

//       await new Promise((resolve) => setTimeout(resolve, 2000));
//       simulateClickOnLocation(map, {
//         lng: customCoordinates.data.coordinates[0],
//         lat: customCoordinates.data.coordinates[1],
//       });

//       map.dragPan.enable();
//       map.scrollZoom.enable();
//       map.doubleClickZoom.enable();
//       map.touchZoomRotate.enable();
//       map.getCanvas().style.cursor = "";

//       handleStorage("session", "ibiCode", "remove");
//       handleStorage("session", "pathHistory", "remove");
//     } catch (error) {
//       console.error("Error navigating to shared location:", error);

//       if (mapRef?.current) {
//         const map = (mapRef?.current as any).getMap();
//         map.dragPan.enable();
//         map.scrollZoom.enable();
//         map.doubleClickZoom.enable();
//         map.touchZoomRotate.enable();
//         map.getCanvas().style.cursor = "";
//       }

//       handleStorage("session", "ibiCode", "remove");
//       handleStorage("session", "pathHistory", "remove");
//     }
//   }, [mapRef]);

//   const {
//     onMouseMove,
//     onMouseLeave,
//     onClick: onClickPropertyManagement,
//     clearSelection: clearPropertySelection,
//     updateStateSelection,
//     propertyLayer,
//     ensureLayerOrder,
//   } = usePropertyManagement();

//   const { forestUrl, alertsUrl, nasaFirmsLayerVisible, fireData, isLoading } = useMapLayers({
//     currentLayers,
//     mapRef: mapRef as React.RefObject<mapboxgl.Map | null>,
//   });
//   ("");
//   const { mapStyle } = useBaseMap();

//   const { resetMapView, handleZoomEnd, zoomToPolygon, setPropertyName } = useMapZoom({
//     mapRef,
//     onMouseMove,
//   });

//   const { getWeatherLayer } = useWeatherLayer();

//   const { fetchForestHistory, setSelectedTerritory } = useForestHistory();

//   const { country } = useDetectCountry();
//   const { setupBoundaries, cleanup, isLoadingCountries, isLoadingStates, findCountryCoordinates } = useWorldBoundaries({
//     onClick: (props) => {
//       if (props) {
//         clearPropertySelection();

//         console.log(`Clicked on ${props.type}: ${props.name}`);
//         setPropertyName(props.name);
//         setPropertyId(props.id);
//         setSelectedTerritory({
//           id: props.id,
//           name: props.name,
//           type: props.type as "country" | "state",
//           country: props.country,
//         });
//         fetchForestHistory(props.id);

//         updateStateSelection(props.type === "state" ? props.id : null);
//       } else {
//         updateStateSelection(null);
//       }
//     },
//   });

//   useEffect(() => {
//     async function setInitialCoordinates() {
//       if (country) {
//         await findCountryCoordinates(country, mapRef?.current as ExtendedMap);
//       }
//     }

//     setInitialCoordinates();
//   }, [country, findCountryCoordinates, mapRef]);

//   useEffect(() => {
//     const map = mapRef?.current;
//     return () => {
//       if (map) {
//         cleanup(map as ExtendedMap);
//       }
//     };
//   }, [cleanup, mapRef]);

//   useEffect(() => {
//     if (mapRef?.current) {
//       handleShareNavigation();
//     }
//     // eslint-disable-next-line react-hooks/exhaustive-deps
//   }, [mapRef?.current, handleShareNavigation]);

//   const onMapLoad = useCallback(
//     (event: any) => {
//       setupBoundaries(event.target as ExtendedMap);

//       const map = event.target;
//       const reorderLayers = () => {
//         if (map.getLayer("properties") && map.getLayer("property-outline")) {
//           map.moveLayer("properties");
//           map.moveLayer("property-outline");
//         }

//         if (map.getLayer("restoration") && map.getLayer("restoration-outline")) {
//           map.moveLayer("restoration");
//           map.moveLayer("restoration-outline");
//         }

//         ensureLayerOrder();
//       };

//       reorderLayers();

//       map.on("style.load", reorderLayers);

//       map.on("sourcedata", (e: any) => {
//         if (e.sourceId === "properties-source" && e.isSourceLoaded) {
//           setTimeout(reorderLayers, 100);
//         }
//       });

//       return () => {
//         map.off("style.load", reorderLayers);
//         map.off("sourcedata", reorderLayers);
//       };
//     },
//     [setupBoundaries, ensureLayerOrder],
//   );

//   const handleMapClick = (e: MapLayerMouseEvent) => {
//     const map = mapRef?.current;
//     if (!map) return;

//     const boundaryFeatures = map.queryRenderedFeatures(e.point, {
//       layers: ["country-fills", "state-fills"],
//     });

//     const propertyFeatures = map.queryRenderedFeatures(e.point, {
//       layers: ["properties"],
//     });

//     if (propertyFeatures.length > 0 && (showProperties || showRestorations)) {
//       clearPropertySelection();
//       onClickPropertyManagement(e);
//       zoomToPolygon(e);
//     } else if (boundaryFeatures.length > 0) {
//       clearPropertySelection();
//     } else {
//       clearPropertySelection();
//       resetMapView();
//       updateStateSelection(null);
//       setPropertyId(null);
//     }
//   };

//   return (
//     <>
//       <div className="relative w-full h-full">
//         <Map
//           ref={mapRef as any}
//           mapStyle={mapStyle}
//           initialViewState={{
//             latitude: -14.72,
//             longitude: -51.419,
//             zoom: INITIAL_COUNTRY_ZOOM,
//           }}
//           padding={padding}
//           mapboxAccessToken={MAPBOX_TOKEN}
//           projection={MAP_PROJECTION}
//           interactiveLayerIds={[
//             ...(showProperties ? ["properties"] : []),
//             ...(showRestorations ? ["restoration"] : []),
//           ]}
//           onLoad={onMapLoad}
//           onMouseMove={(e) => {
//             if ((showProperties || showRestorations) && e.features && e.features.length > 0) {
//               onMouseMove(e as MapLayerMouseEvent);
//             }
//           }}
//           style={{ width: "100%", height: "100%" }}
//           onMouseLeave={onMouseLeave}
//           onClick={handleMapClick}
//           onZoomEnd={handleZoomEnd}
//         >
//           {(isLoadingCountries || isLoadingStates || isLoading || weatherLoading) && (
//             <div className="absolute top-[250px] left-20 z-10">
//               <LoadingSpinner variant="primary" size="sm" text="Loading map data..." direction="row" />
//             </div>
//           )}
//           {showProperties && (
//             <Source
//               id="properties-source"
//               type="vector"
//               tiles={[`https://tiles.yby.energy/dev/properties-${propertyId}/{z}/{x}/{y}`]}
//               minzoom={PROPERTIES_MIN_ZOOM}
//               maxzoom={PROPERTIES_MAX_ZOOM}
//               generateId={true}
//             >
//               <Layer {...propertyLayer} />
//               <Layer {...propertyOutlineLayer} />
//             </Source>
//           )}

//           {forestUrl && (
//             <Source id="forest-source" type="raster" tiles={[forestUrl]} tileSize={256}>
//               <Layer
//                 id="forest-layer"
//                 type="raster"
//                 paint={{
//                   "raster-opacity": 0.7,
//                 }}
//               />
//             </Source>
//           )}

//           {alertsUrl && (
//             <Source id="alerts-source" type="raster" tiles={[alertsUrl]} tileSize={256}>
//               <Layer
//                 id="alerts-layer"
//                 type="raster"
//                 paint={{
//                   "raster-opacity": 0.7,
//                 }}
//               />
//             </Source>
//           )}

//           {currentLayers.includes("movebank") && <MovebankLayer visible={true} pointColor="#FF9800" pointSize={5} />}

//           {weatherError && (
//             <div className="absolute bottom-10 left-10 bg-red-500 text-white p-2 rounded">
//               Error loading weather data: {weatherError.message}
//             </div>
//           )}
//           <WindLayer />
//           <PrecipitationLayer />
//           <CloudsLayer />
//           <TemperatureLayer />
//           <PressureLayer />

//           {nasaFirmsLayerVisible && fireData && (
//             <Source id="fire-data" type="geojson" data={fireData}>
//               <Layer
//                 id="fire-layer"
//                 type="circle"
//                 paint={{
//                   "circle-radius": 5,
//                   "circle-color": "#ff0000",
//                   "circle-stroke-width": 1,
//                   "circle-stroke-color": "#ffffff",
//                 }}
//               />
//             </Source>
//           )}

//           {getWeatherLayer()}

//           {/* {popupInfo && (
//             <Popup
//               style={{
//                 pointerEvents: "none",
//                 margin: "0 0 3em 2em",
//                 transition: "all 0.1s ease-out",
//                 touchAction: "none",
//               }}
//               longitude={popupInfo.lngLat.lng}
//               latitude={popupInfo.lngLat.lat}
//               closeButton={false}
//               closeOnClick={true}
//               onClose={() => setPopupInfo(null)}
//               anchor="bottom"
//             >
//               <PopupContent popupInfo={popupInfo} />
//             </Popup>
//           )} */}
//         </Map>
//       </div>
//     </>
//   );
// };

// export default IbiMap;
