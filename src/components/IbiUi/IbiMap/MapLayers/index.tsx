import IbiIcon from "@/components/IbiUi/IbiIcon";
import IbiSwitch from "@/components/IbiUi/IbiSwitch";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import useMap from "@/hooks/useMap";
import { cn } from "@/lib/utils";
import { CurrentLayers } from "@/types";
import React from "react";

const MapLayers = () => {
  const { currentLayers, setCurrentLayers } = useMap();

  const layerOptions = [
    {
      id: 2,
      label: "Forest",
      icon: "mdi:tree-outline",
      iconSize: "text-lg",
      mapState: "forest" as CurrentLayers,
      bgColorClass: "bg-[#2E865F]",
    },
    {
      id: 3,
      label: "Alerts",
      icon: "alerts-switch",
      iconSize: "text-lg",
      mapState: "alerts" as CurrentLayers,
      bgColorClass: "bg-[#FFC107]",
    },
    {
      id: 4,
      label: "Wind",
      icon: "meteocons:wind",
      iconSize: "text-lg",
      mapState: "wind" as CurrentLayers,
      bgColorClass: "bg-[#66D9EF]",
    },
    {
      id: 7,
      label: "Precipitation",
      icon: "mdi:weather-pouring",
      iconSize: "text-lg",
      mapState: "precipitation" as CurrentLayers,
      bgColorClass: "bg-[#3498DB]",
    },
    {
      id: 8,
      label: "Clouds",
      icon: "mdi:weather-cloudy",
      iconSize: "text-lg",
      mapState: "clouds" as CurrentLayers,
      bgColorClass: "bg-[#A5B4C2]",
    },
    {
      id: 9,
      label: "Temperature",
      icon: "mdi:thermometer",
      iconSize: "text-lg",
      mapState: "temperature" as CurrentLayers,
      bgColorClass: "bg-[#E74C3C]",
    },
    {
      id: 10,
      label: "Pressure",
      icon: "mdi:gauge",
      iconSize: "text-lg",
      mapState: "pressure" as CurrentLayers,
      bgColorClass: "bg-[#9B59B6]",
    },
    {
      id: 5,
      label: "Fire (NASA Firms)",
      icon: "lineicons:nasa",
      iconSize: "text-lg",
      mapState: "nasa" as CurrentLayers,
      bgColorClass: "bg-[#FFC107]",
    },
    {
      id: 6,
      label: "Animal Tracking",
      icon: "mdi:paw",
      iconSize: "text-lg",
      mapState: "movebank" as CurrentLayers,
      bgColorClass: "bg-[#FF9800]",
    },
  ];

  return (
    <Popover>
      <PopoverTrigger asChild>
        <button className="border border-[#ffffff56] hover:border-[#ffffff] hover:bg-[#ffffff56] overflow-hidden rounded-md h-[40px] w-fit bg-primary-dark flex items-center justify-center px-2 gap-2">
          <IbiIcon icon="token-branded:apex-layer" className="text-xl" />
          Show layers
        </button>
      </PopoverTrigger>
      <PopoverContent
        sideOffset={12}
        side="right"
        align="start"
        className="w-[298px] rounded-none border-none p-[1px] bg-[#ffffff2f] layers-box-clip-path relative text-white"
      >
        <div className="layers-box-clip-path bg-[#0A0E15] size-full px-4 pb-6 pt-5">
          <h1 className="text-gray-400 text-[12px]">Layers</h1>
          <div className="mt-3 flex flex-col gap-5">
            {layerOptions.map((option) => (
              <div key={option.id} className="flex justify-between">
                <div className="flex gap-2 items-center">
                  <div className={cn("size-[20px] border border-white", option.bgColorClass)} />
                  <div className="flex items-center gap-2">
                    <h2 className="text-gray-400 text-[14px]">{option.label}</h2>
                    {/* TODO: handle loading state later */}
                    {/* {(option.mapState === "wind" ||
                      option.mapState === "precipitation" ||
                      option.mapState === "clouds" ||
                      option.mapState === "temperature" ||
                      option.mapState === "pressure") &&
                      currentLayers?.includes(option.mapState) && (
                        <IbiIcon icon="svg-spinners:180-ring" className="text-sm animate-spin" />
                      )} */}
                  </div>
                </div>
                <IbiSwitch
                  id={`layer-${option.id}`}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setCurrentLayers((prev) => [...prev, option.mapState]);
                    } else {
                      setCurrentLayers((prev) => prev.filter((layer) => layer !== option.mapState));
                    }
                  }}
                  checked={currentLayers?.includes(option.mapState)}
                />
              </div>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default MapLayers;
