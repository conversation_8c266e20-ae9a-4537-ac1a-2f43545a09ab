// import { Badge } from "@/components/ui/badge";
// import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from "@/components/ui/sheet";
// import useMap from "@/hooks/useMap";
// import type { CurrentMap } from "@/types";
// import React, { useState } from "react";

// import IbiIcon from "../../IbiIcon";
// import IbiSwitch from "../../IbiSwitch";

// const MapSettings = () => {
//   const [open, setOpen] = useState(false);
//   const { currentMap, setCurrentMap } = useMap();

//   const handleMapChange = (map: CurrentMap) => setCurrentMap(map);

//   const tomorrowMapTypes: Record<CurrentMap, Record<string, string>> = {
//     default: {
//       name: "Default",
//       icon: "carbon:map",
//     },
//     "global-precipitation": {
//       name: "Precipitation Intensity",
//       icon: "fluent-mdl2:precipitation",
//     },
//     temperature: {
//       name: "Temperature Areas",
//       icon: "carbon:temperature-hot",
//     },
//     fire: {
//       name: "Fire Areas",
//       icon: "streamline-emojis:fire",
//     },
//     windSpeed: {
//       name: "Wind Speed",
//       icon: "game-icons:wind-hole",
//     },
//     ozoneSurfaceConcentration: {
//       name: "Ozone Concentration",
//       icon: "game-icons:habitat-dome",
//     },
//     wind: {
//       name: "Wind Areas",
//       icon: "meteocons:wind-snow",
//     },
//     nasaFireLayer: {
//       name: "Nasa Firms Fire Information",
//       icon: "simple-icons:nasa",
//     },
//   };

//   return (
//     <>
//       <Sheet open={open} onOpenChange={setOpen}>
//         <SheetTrigger>
//           <div className="absolute bottom-0 left-0 mb-36 ml-3 overflow-hidden">
//             <div className="bg-black p-1.5 text-white">
//               <IbiIcon icon="carbon:settings" />
//             </div>
//           </div>
//         </SheetTrigger>
//         <SheetContent side="left" className="bg-[#000] w-[280px] border-0 flex flex-col">
//           <SheetHeader>
//             <SheetTitle className="text-white flex items-center gap-1">
//               <IbiIcon icon="hugeicons:square-arrow-data-transfer-diagonal" />
//               Customize your map
//             </SheetTitle>
//           </SheetHeader>
//           <div className="flex flex-col flex-1">
//             <h1>
//               <strong>Layers:</strong> <span className="text-gray-400">what layers to show</span>
//             </h1>
//             <div className="mt-3 flex flex-col gap-3">
//               <IbiSwitch
//                 id="wind-layer"
//                 label="Wind"
//                 checked={currentMap === "wind"}
//                 onCheckedChange={(e) => handleMapChange(e ? "wind" : "default")}
//               />
//               <IbiSwitch
//                 id="global-precipitation"
//                 label="Global Precipitation"
//                 checked={currentMap === "global-precipitation"}
//                 onCheckedChange={(e) => handleMapChange(e ? "global-precipitation" : "default")}
//               />
//               <IbiSwitch
//                 id="temperature"
//                 label="Temperature"
//                 checked={currentMap === "temperature"}
//                 onCheckedChange={(e) => handleMapChange(e ? "temperature" : "default")}
//               />
//               <IbiSwitch
//                 id="fire"
//                 label="Fire"
//                 checked={currentMap === "fire"}
//                 onCheckedChange={(e) => handleMapChange(e ? "fire" : "default")}
//               />
//               <IbiSwitch
//                 id="windSpeed"
//                 label="Wind Speed"
//                 checked={currentMap === "windSpeed"}
//                 onCheckedChange={(e) => handleMapChange(e ? "windSpeed" : "default")}
//               />
//               <IbiSwitch
//                 id="ozoneSurfaceConcentration"
//                 label="Ozone Surface Concentration"
//                 checked={currentMap === "ozoneSurfaceConcentration"}
//                 onCheckedChange={(e) => handleMapChange(e ? "ozoneSurfaceConcentration" : "default")}
//               />
//               <IbiSwitch
//                 id="nasaFireLayer"
//                 label="Nasa Firms Fire Information"
//                 checked={currentMap === "nasaFireLayer"}
//                 onCheckedChange={(e) => handleMapChange(e ? "nasaFireLayer" : "default")}
//               />
//             </div>
//           </div>
//         </SheetContent>
//       </Sheet>
//       <div className="absolute top-0 right-0 m-2 overflow-hidden rounded-xl">
//         {currentMap !== "default" && (
//           <Badge className="flex gap-2 bg-secondary-alternative px-3 py-1.5">
//             <IbiIcon icon={tomorrowMapTypes[currentMap].icon} className="text-lg" />
//             <h1 className="text-[14px]">{tomorrowMapTypes[currentMap].name}</h1>
//             <IbiIcon icon="carbon:close" className="text-xl" btn onClick={() => handleMapChange("default")} />
//           </Badge>
//         )}
//       </div>
//     </>
//   );
// };

// export default MapSettings;
