import { CircularChartProps } from "@/types";
import { AnimatePresence, motion } from "framer-motion";
import React, { useMemo, useState } from "react";
import { <PERSON>, Pie, PieChart, ResponsiveContainer, Tooltip } from "recharts";

import IbiIcon from "../../IbiIcon";

const COLORS = ["#40E0D0", "#8A2BE2", "#FFA500", "#FF6B6B", "#4ECDC4", "#50C878", "#9370DB", "#FF7F50"];

const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-[#1A1A1A] p-2 rounded-md border border-gray-700">
        <p className="text-gray-200">{payload[0].payload.name}</p>
        <p className="text-gray-400">{payload[0].payload.label}</p>
        <p className="text-gray-300 font-bold">{payload[0].payload.percentage}%</p>
      </div>
    );
  }
  return null;
};

const DoughnutChart = ({ data, isLoading, territoryName }: CircularChartProps) => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);

  const hasEcoregions = data && data.ecoregions && Array.isArray(data.ecoregions) && data.ecoregions.length > 0;

  const totalForestArea = useMemo(() => {
    if (!hasEcoregions) return 0;
    return data?.ecoregions?.reduce((sum, eco) => sum + eco.forestArea, 0);
  }, [data, hasEcoregions]);

  interface ChartDataItem {
    id: string;
    name: string;
    value: number;
    fill: string;
    label: string;
    percentage: string;
  }

  const chartData = useMemo<ChartDataItem[]>(() => {
    if (!hasEcoregions || !data?.ecoregions) {
      return [];
    }

    return data.ecoregions
      .map((item: any, index) => {
        if (!item || typeof item.forestArea !== "number") {
          console.warn("Invalid ecoregion item:", item);
          return null;
        }

        return {
          id: item.id || String(index),
          name: item.name || `Ecoregion ${index + 1}`,
          value: item.forestArea,
          fill: COLORS[index % COLORS.length],
          label: `${item.forestArea.toFixed(2)} ha`,
          percentage: totalForestArea ? ((item.forestArea / totalForestArea) * 100).toFixed(1) : "0",
        };
      })
      .filter((item): item is ChartDataItem => item !== null)
      .sort((a, b) => b.value - a.value);
  }, [data, hasEcoregions, totalForestArea]);

  const handleMouseEnter = (_: any, index: number) => {
    setActiveIndex(index);
  };

  const handleMouseLeave = () => {
    setActiveIndex(null);
  };

  const getActivePercentage = () => {
    if (activeIndex !== null && chartData[activeIndex] && totalForestArea && totalForestArea > 0) {
      return chartData[activeIndex].percentage;
    }
    return null;
  };

  if (isLoading) {
    return (
      <div className="flex flex-col w-full text-xs">
        <h3 className="text-xs text-gray-400 font-bold uppercase">FOREST AREAS</h3>
        <div className="flex-1 flex items-center justify-center mt-7">
          <div className="text-gray-400">Loading forest area data...</div>
        </div>
      </div>
    );
  }

  if (!isLoading && chartData.length === 0) {
    return (
      <div className="flex flex-col w-full text-xs">
        <h3 className="text-xs text-gray-400 font-bold uppercase">FOREST AREAS</h3>
        <p className="text-gray-500 mt-4">No forest area data available</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full text-xs">
      <h3 className="text-xs text-gray-400 font-bold uppercase">
        FOREST AREAS {territoryName && `- ${territoryName}`}
      </h3>

      <div className="flex flex-col items-center mt-7">
        <div className="size-[260px] relative mb-6">
          <ResponsiveContainer width={260} height={260}>
            <PieChart>
              <Pie
                data={chartData}
                cx={130}
                cy={130}
                innerRadius={60}
                outerRadius={95}
                dataKey="value"
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
                animationBegin={0}
                animationDuration={800}
                isAnimationActive={true}
                cornerRadius={2}
                paddingAngle={0}
                strokeWidth={1}
              >
                {chartData.map((entry, index) => (
                  <Cell
                    key={`arc-${index}`}
                    fill={`${entry.fill}33`} // Adding 80 hex for 50% opacity
                    stroke={entry.fill}
                    className="transition-all duration-300 ease-in-out"
                    style={{
                      filter: activeIndex === index ? "drop-shadow(0 0 8px rgba(255,255,255,0.2))" : "none",
                      opacity: activeIndex === null || activeIndex === index ? 1 : 0.3,
                    }}
                  />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>

          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
            {getActivePercentage() ? (
              <>
                <div className="text-2xl font-bold text-white">{getActivePercentage()}%</div>
                <div className="text-xs text-gray-400">of total</div>
              </>
            ) : (
              <>
                <div className="text-sm font-bold text-white">{totalForestArea?.toFixed(2)} ha</div>
                <div className="text-xs text-gray-400">total area</div>
              </>
            )}
          </div>
        </div>

        <div className="w-full max-w-md mx-auto">
          <AnimatePresence initial={false}>
            <motion.div
              className={`grid gap-x-6 gap-y-3 w-full justify-center ${
                chartData.length <= 2
                  ? "grid-cols-2"
                  : chartData.length <= 3
                    ? "grid-cols-3"
                    : chartData.length <= 4
                      ? "grid-cols-2"
                      : "grid-cols-3"
              }`}
            >
              {chartData.slice(0, isExpanded ? chartData.length : Math.min(6, chartData.length)).map((entry, index) => (
                <button
                  key={entry.id}
                  className="flex items-center gap-2 text-left hover:opacity-80 transition-opacity duration-300 justify-center"
                  onMouseEnter={() => setActiveIndex(index)}
                  onMouseLeave={() => setActiveIndex(null)}
                >
                  <div
                    className="min-w-3 h-3 rounded"
                    style={{
                      backgroundColor: entry.fill,
                      transition: "all 400ms ease-in-out",
                      opacity: activeIndex === null || activeIndex === index ? 1 : 0.3,
                      transform: activeIndex === index ? "scale(1.2)" : "scale(1)",
                      boxShadow: activeIndex === index ? `0 0 12px ${entry.fill}CC` : "none",
                    }}
                  />
                  <div className="flex flex-col truncate">
                    <span className="text-gray-200 flex items-center">
                      <span className="truncate">{entry.label}</span>
                      <small className="text-gray-400 whitespace-nowrap ml-1">({entry.percentage}%)</small>
                    </span>
                    <span className="text-gray-500 text-xs truncate">{entry.name}</span>
                  </div>
                </button>
              ))}
            </motion.div>
          </AnimatePresence>

          {chartData.length > 6 && (
            <motion.div
              className="mt-4 flex justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-gray-400 hover:text-white transition-colors duration-300 text-sm bg-gray-800/30 px-1"
              >
                <motion.div initial={false} animate={{ rotate: isExpanded ? 180 : 0 }} transition={{ duration: 0.3 }}>
                  <IbiIcon icon="ion:chevron-down" className="text-lg mt-1.5" />
                </motion.div>
              </button>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DoughnutChart;
