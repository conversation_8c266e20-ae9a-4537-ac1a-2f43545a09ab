import LoadingSpinner from "@/components/LoadingSpinner";
import { Slider } from "@/components/ui/slider";
import { CircularChartProps, CustomTooltipProps } from "@/types";
import { motion } from "framer-motion";
import React, { useCallback, useMemo, useState } from "react";
import { Bar, BarChart, Cell, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts";

const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    const areaHa = Number(data.areaHa);
    const year = data.year;

    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2 }}
        className="bg-[#1A1A1A] p-3 rounded-md border border-gray-700 shadow-lg min-w-[180px]"
      >
        <h4 className="text-gray-200 font-bold text-sm border-b border-gray-700 pb-1 mb-2">Forest Area {year}</h4>
        <div className="space-y-1">
          <p className="text-gray-200 font-medium">{areaHa.toLocaleString("en-US", { maximumFractionDigits: 2 })} ha</p>
          <p className="text-gray-400 text-xs flex justify-between">
            <span>Year:</span>
            <span className="text-gray-300">{year}</span>
          </p>
        </div>
      </motion.div>
    );
  }
  return null;
};

const ForestAreaChart = ({ data, isLoading, territoryName }: CircularChartProps) => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [rangeValue, setRangeValue] = useState<[number, number]>([0, 100]);

  const handleMouseEnter = (_: any, index: number) => {
    setActiveIndex(index);
  };

  const handleMouseLeave = () => {
    setActiveIndex(null);
  };

  const handleRangeChange = (value: [number, number]) => {
    setRangeValue(value);
  };

  const hasData = data && data.forestHistory && Array.isArray(data.forestHistory) && data.forestHistory.length > 0;

  const processedData = useMemo(() => {
    if (!hasData) return [];

    if (data?.isPropertyData) {
      return data.forestHistory.map((entry) => ({
        year: entry.year,
        areaHa: Number(entry.areaHa),
      }));
    }

    return (
      data?.forestHistory?.map((entry) => {
        const totalArea = entry?.ecoregions?.reduce((sum, eco) => sum + eco.areaHa, 0);

        return {
          year: entry.year,
          areaHa: totalArea,
        };
      }) || []
    );
  }, [data, hasData]);

  const sortedData = useMemo(() => {
    return [...processedData].sort((a, b) => a.year - b.year);
  }, [processedData]);

  const rangeIndices = useMemo(() => {
    if (sortedData.length === 0) return { startIdx: 0, endIdx: 0 };

    const startIdx = Math.floor(sortedData.length * (rangeValue[0] / 100));
    const endIdx = Math.ceil(sortedData.length * (rangeValue[1] / 100));

    return { startIdx, endIdx };
  }, [sortedData, rangeValue]);

  const isInSelectedRange = useCallback(
    (_: any, index: number) => {
      return index >= rangeIndices.startIdx && index < rangeIndices.endIdx;
    },
    [rangeIndices],
  );

  if (isLoading) {
    return (
      <div className="flex flex-col w-full h-[200px] text-xs">
        <h3 className="text-xs text-gray-400 font-bold uppercase mb-4">HISTORICAL FOREST AREA</h3>
        <div className="flex-1 flex items-center justify-center">
          <LoadingSpinner variant="primary" size="sm" text="Loading forest history data..." direction="row" />
        </div>
      </div>
    );
  }

  if (!isLoading && (!data || !hasData)) {
    return (
      <div className="flex flex-col w-full h-[200px] text-xs">
        <h3 className="text-xs text-gray-400 font-bold uppercase mb-4">HISTORICAL FOREST AREA</h3>
        <div className="flex-1 flex items-center justify-center text-gray-400">No forest history data available</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full h-[320px] text-xs">
      <h3 className="text-xs text-gray-400 font-bold uppercase mb-4">
        HISTORICAL FOREST AREA {territoryName && `- ${territoryName}`}
      </h3>

      <div
        className="flex-1"
        style={{
          backgroundImage:
            "linear-gradient(rgba(20, 20, 20, 0.5) 1px, transparent 1px), linear-gradient(90deg, rgba(20, 20, 20, 0.5) 1px, transparent 1px)",
          backgroundSize: "20px 20px",
        }}
      >
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={sortedData} margin={{ top: 5, right: 15, bottom: 30, left: 10 }} barCategoryGap={1}>
            <XAxis
              dataKey="year"
              axisLine={{ stroke: "#333" }}
              tickLine={false}
              tick={{ fill: "#666", fontSize: 10 }}
              interval={1}
              dy={10}
              angle={-45}
              textAnchor="end"
            />
            <YAxis
              axisLine={{ stroke: "#333" }}
              tickLine={false}
              tick={{ fill: "#666", fontSize: 10 }}
              dx={0}
              tickFormatter={(value) => {
                const formatted = (Number(value) / 1000).toFixed(0);
                return formatted === "0" ? "" : `${formatted}k`;
              }}
              domain={["dataMin", "dataMax"]}
              width={45}
            />
            <Tooltip content={<CustomTooltip />} cursor={{ fill: "transparent" }} />
            <Bar
              dataKey="areaHa"
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
              radius={[2, 2, 0, 0]}
              animationDuration={800}
              animationEasing="ease-in-out"
              isAnimationActive={true}
            >
              {sortedData.map((entry, index) => {
                const inRange = isInSelectedRange(entry, index);
                const isActive = activeIndex === index;

                const fillColor = isActive
                  ? inRange
                    ? "#15803d80"
                    : "#33333380"
                  : inRange
                    ? "#15803d33"
                    : "#33333333";

                const strokeColor = isActive ? (inRange ? "#15803dff" : "#555555") : inRange ? "#15803d" : "#333333";

                const strokeWidth = isActive ? 2 : 1;

                return <Cell key={`cell-${index}`} fill={fillColor} stroke={strokeColor} strokeWidth={strokeWidth} />;
              })}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>

      <div>
        <div className="relative pb-5">
          <Slider
            defaultValue={[0, 100]}
            value={rangeValue}
            onValueChange={handleRangeChange}
            max={100}
            step={1}
            className="w-full"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-2 px-1">
            <span>{sortedData.length > 0 ? sortedData[0].year : "N/A"}</span>
            <span>{sortedData.length > 0 ? sortedData[sortedData.length - 1].year : "N/A"}</span>
          </div>
          <div className="text-center text-xs text-gray-400 mt-2">
            <span>Drag the slider to zoom in on a specific time period</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForestAreaChart;
