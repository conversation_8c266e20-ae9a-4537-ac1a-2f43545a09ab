import { Skeleton } from "@/components/ui/skeleton";

const PropertySkeleton = () => {
  return (
    <div className="mt-3 animate-in fade-in-50">
      <h2 className="text-xs text-gray-400 font-bold uppercase">PROPERTY DETAILS</h2>
      <div className="mt-4 space-y-4">
        <div>
          <div className="flex items-center gap-1">
            <h3 className="text-lg text-gray-400">IBI Code</h3>
          </div>
          <Skeleton className="h-7 w-32 bg-gray-800" />
        </div>
        <div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-400">Total Forest Area</span>
            <Skeleton className="h-7 w-24 bg-gray-800" />
          </div>
        </div>
        <div className="space-y-2">
          <Skeleton className="h-[200px] w-full bg-gray-800" />
        </div>
        <Skeleton className="h-10 w-full bg-gray-800" />

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Skeleton className="h-5 w-24 bg-gray-800" />
            <Skeleton className="h-4 w-12 bg-gray-800" />
          </div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-16 w-full bg-gray-800" />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PropertySkeleton;
