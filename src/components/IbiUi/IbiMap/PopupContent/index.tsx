// "use client";

// import HoverCard from "@/components/HoverCard";
// import LoadingSpinner from "@/components/LoadingSpinner";
// import { usePropertyDetails } from "@/queries/hooks/useTerritories";
// import { PopupInfo } from "@/types";

// export const PopupContent = ({ popupInfo }: { popupInfo: PopupInfo }) => {
//   const { data: propertyDetails, isLoading, error } = usePropertyDetails(popupInfo.propertyId as number);

//   if (isLoading) {
//     return <LoadingSpinner size="sm" text="Loading property details..." />;
//   }

//   if (error) {
//     return (
//       <HoverCard title="Error">
//         <div className="text-red-400">Failed to load property details</div>
//       </HoverCard>
//     );
//   }

//   if (!propertyDetails) {
//     return null;
//   }

//   const { ibiCode, ecoregions, totalForestArea } = propertyDetails;

//   return (
//     <HoverCard title={`Property: ${ibiCode}`}>
//       <div className="flex flex-col gap-3 whitespace-nowrap">
//         <div className="space-y-2">
//           <h4 className="font-bold text-white">Forest Areas</h4>
//           {ecoregions?.map((eco, index) => (
//             <div key={index} className="flex items-center text-white">
//               <span className="inline-block">{eco?.name}</span>
//               <span className="inline-block ml-auto pl-6 tabular-nums">{eco?.forestArea?.toFixed(2)} ha</span>
//             </div>
//           ))}
//           <div className="flex items-center border-t border-[#ffffff30] pt-2 mt-2 text-white">
//             <strong className="inline-block">Total Forest Area:</strong>
//             <span className="inline-block ml-auto pl-6 tabular-nums">{totalForestArea?.toFixed(2)} ha</span>
//           </div>
//         </div>
//       </div>
//     </HoverCard>
//   );
// };
