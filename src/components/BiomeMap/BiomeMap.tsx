import { useBaseMap } from "@/hooks/useBaseMap";
import { useBiomeMap } from "@/hooks/useLayerControl/useBiomeMap";
import useMap from "@/hooks/useMap";
import { useMapSelector } from "@/stores/map.store";
import { INITIAL_COUNTRY_ZOOM, MAPBOX_TOKEN, MAP_PROJECTION } from "@/utils/constants";
import "mapbox-gl/dist/mapbox-gl.css";
import React, { useCallback, useEffect } from "react";
import Map from "react-map-gl";

import LoadingState from "../shared/LoadingState";

const BiomeMap: React.FC = () => {
  const { mapRef } = useMap();
  const { mapStyle } = useBaseMap();
  const { activeLayers, isLoading, initializeLayers, cleanup } = useBiomeMap();
  const padding = useMapSelector.use.padding();

  useEffect(() => {
    if (mapRef?.current) {
      initializeLayers();
    }

    return () => {
      cleanup();
    };
  }, [mapRef, initializeLayers, cleanup]);

  const onMapLoad = useCallback(() => {
    if (mapRef?.current) {
      const map = (mapRef.current as any).getMap();

      const style = map.getStyle();
      style.layers.forEach((layer) => {
        if (layer.id.includes("boundary") || layer.id.includes("admin") || layer.id.includes("border")) {
          map.setLayoutProperty(layer.id, "visibility", "none");
        }
      });

      initializeLayers();
    }
  }, [mapRef, initializeLayers]);

  return (
    <div className="relative w-full h-full">
      <Map
        ref={mapRef as any}
        mapStyle={mapStyle}
        initialViewState={{
          latitude: -14.72,
          longitude: -51.419,
          zoom: INITIAL_COUNTRY_ZOOM,
        }}
        padding={padding}
        mapboxAccessToken={MAPBOX_TOKEN}
        projection={MAP_PROJECTION}
        onLoad={onMapLoad}
        style={{ width: "100%", height: "100%" }}
        interactiveLayerIds={[
          ...(activeLayers.showBiomes ? ["biomes-fill"] : []),
          ...(activeLayers.showEcoregions ? ["ecoregions-fill"] : []),
        ]}
      >
        {isLoading && (
          <div className="absolute top-[150px] left-20 z-10">
            <LoadingState variant="inline" text="Loading map data..." />
          </div>
        )}
      </Map>
    </div>
  );
};

export default BiomeMap;
