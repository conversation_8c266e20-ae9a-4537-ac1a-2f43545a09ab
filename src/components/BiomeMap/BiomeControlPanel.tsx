import IbiIcon from "@/components/IbiUi/IbiIcon";
import { BiomeLayer, DataType, EcoregionLayer } from "@/hooks/useLayerControl/useBiomeMap";
import { cn } from "@/lib/utils";
import React from "react";

interface BiomeControlPanelProps {
  biomeLayers: BiomeLayer[];
  ecoregionLayers: EcoregionLayer[];
  activeLayers: {
    showBiomes: boolean;
    showEcoregions: boolean;
  };
  selectedItem: {
    id: string | null;
    type: DataType | null;
    properties: any | null;
  };
  isLoading: boolean;
  onToggleLayer: (type: DataType, visible: boolean) => void;
  onReset: () => void;
}

const BiomeControlPanel: React.FC<BiomeControlPanelProps> = ({
  biomeLayers,
  ecoregionLayers,
  activeLayers,
  selectedItem,
  isLoading,
  onToggleLayer,
  onReset,
}) => {
  const biomeCount = biomeLayers.length;
  const ecoregionCount = ecoregionLayers.length;

  return (
    <div className="absolute top-0 right-0 mt-[140px] mr-[30px] w-[300px] bg-primary-dark bg-opacity-90 rounded-lg shadow-lg text-white p-4 z-10">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold">Ecological Layers</h2>
        {isLoading && (
          <div className="flex items-center">
            <IbiIcon icon="svg-spinners:270-ring" className="text-xl animate-spin mr-2" />
            <span className="text-sm">Loading...</span>
          </div>
        )}
      </div>

      <div className="space-y-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only"
                checked={activeLayers.showBiomes}
                onChange={(e) => onToggleLayer("biomes", e.target.checked)}
              />
              <div
                className={cn(
                  "w-10 h-5 rounded-full transition-colors",
                  activeLayers.showBiomes ? "bg-green-500" : "bg-gray-600",
                )}
              >
                <div
                  className={cn(
                    "transform transition-transform w-4 h-4 bg-white rounded-full shadow-md translate-x-0.5 translate-y-0.5",
                    activeLayers.showBiomes && "translate-x-5",
                  )}
                />
              </div>
              <span className="ml-2 flex-1">Biomes</span>
            </label>
            <span className="text-xs text-gray-400">{biomeCount} items</span>
          </div>

          <div className="flex items-center justify-between">
            <label className="flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only"
                checked={activeLayers.showEcoregions}
                onChange={(e) => onToggleLayer("ecoregions", e.target.checked)}
              />
              <div
                className={cn(
                  "w-10 h-5 rounded-full transition-colors",
                  activeLayers.showEcoregions ? "bg-green-500" : "bg-gray-600",
                )}
              >
                <div
                  className={cn(
                    "transform transition-transform w-4 h-4 bg-white rounded-full shadow-md translate-x-0.5 translate-y-0.5",
                    activeLayers.showEcoregions && "translate-x-5",
                  )}
                />
              </div>
              <span className="ml-2 flex-1">Ecoregions</span>
            </label>
            <span className="text-xs text-gray-400">{ecoregionCount} items</span>
          </div>
        </div>

        {selectedItem.id && (
          <div className="mt-6 border-t border-gray-700 pt-4">
            <h3 className="font-medium mb-2">Selected {selectedItem.type}</h3>
            <div className="bg-gray-800 p-3 rounded-md text-sm">
              <p className="mb-1">
                <span className="text-gray-400">Name:</span> {selectedItem.properties?.name}
              </p>
              <p className="mb-1">
                <span className="text-gray-400">ID:</span> {selectedItem.id}
              </p>
              {selectedItem.type === "ecoregions" && selectedItem.properties?.biomeId && (
                <p>
                  <span className="text-gray-400">Biome ID:</span> {selectedItem.properties.biomeId}
                </p>
              )}

              {selectedItem.properties?.area && (
                <p>
                  <span className="text-gray-400">Area:</span> {selectedItem.properties.area.toLocaleString()} km²
                </p>
              )}
            </div>
          </div>
        )}

        <div className="mt-4">
          <button
            onClick={onReset}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center justify-center"
          >
            <IbiIcon icon="mdi:refresh" className="mr-2" />
            Reset View
          </button>
        </div>
      </div>
    </div>
  );
};

export default BiomeControlPanel;
