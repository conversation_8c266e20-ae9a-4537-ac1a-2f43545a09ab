import MapControllers from "@/components/IbiUi/IbiMap/MapControllers";
import { useHydroshedsManagementStore } from "@/hooks/useLayerControl/useHydrosheds/store";
import useMap from "@/hooks/useMap";
import { useMapSelector } from "@/stores/map.store";
import React, { useEffect, useRef } from "react";
import { lazy } from "react";

import IbiMap from "../IbiUi/IbiMap";

const MapLayers = lazy(() => import("@/components/IbiUi/IbiMap/MapLayers"));

interface MapContainerProps {
  currentMapType: "default" | "biome" | "hydro";
}

const MapContainer: React.FC<MapContainerProps> = ({ currentMapType }) => {
  const { setShowProperties, setShowRestorations } = useMap();
  const triggerHydroshedsReload = useMapSelector.use.triggerHydroshedsReload();
  const { setCurrentHydroshedLevel, setSelectedHydroshedLevel } = useHydroshedsManagementStore();
  const previousMapTypeRef = useRef<string | null>(null);
  const setSelectedHydroshedId = useMapSelector.use.setSelectedHydroshedId();

  useEffect(() => {
    const isChangingToHydro = currentMapType === "hydro" && previousMapTypeRef.current !== "hydro";
    previousMapTypeRef.current = currentMapType;

    if (currentMapType === "default") {
      setShowProperties(true);
      setShowRestorations(false);
    } else {
      setShowProperties(false);
      setShowRestorations(false);

      if (isChangingToHydro) {
        setSelectedHydroshedLevel({
          id: "hybas_12",
          label: "Hybas 12",
        });
        setCurrentHydroshedLevel("Hybas 12 (default)");
        setSelectedHydroshedId(null);

        setTimeout(() => {
          triggerHydroshedsReload();
        }, 500);
      }
    }
  }, [
    currentMapType,
    setShowProperties,
    setShowRestorations,
    triggerHydroshedsReload,
    setSelectedHydroshedLevel,
    setCurrentHydroshedLevel,
    setSelectedHydroshedId,
  ]);

  return (
    <div className="w-full h-screen relative">
      <>
        <IbiMap />
        <MapControllers />

        <div className="absolute top-0 left-0 mt-[60px] sm:mt-[80px] ml-[10px] sm:ml-[75px] z-10">
          <MapLayers />
        </div>
      </>
    </div>
  );
};

export default MapContainer;
