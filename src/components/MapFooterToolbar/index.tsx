import IbiIcon from "@/components/IbiUi/IbiIcon";
import React from "react";

import { Ta<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from "../ui/tabs";

interface MapFooterToolbarProps {
  className?: string;
}

const MapFooterToolbar: React.FC<MapFooterToolbarProps> = ({ className }) => {
  return (
    <div
      className={`w-full text-xs bg-black/80 backdrop-blur-md border-t border-t-[#ffffff1a] py-1 px-4 pl-[62px] flex justify-between items-center ${className}`}
    >
      <div className="flex items-center gap-3">
        <div className="flex gap-1.5 items-center">
          <span className="relative flex size-[10px]">
            <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-red-400 opacity-75"></span>
            <span className="relative inline-flex size-[10px] rounded-full bg-red-500"></span>
          </span>
          <span className="text-red-500 font-semibold">Inactive</span>
        </div>
        <hr className="h-[10px] border-[#ffffff5e] border-l-[0.1rem]" />
        <button className="text-white/80 hover:text-white flex items-center gap-1.5">
          <span>Terms of Service</span>
        </button>
        <hr className="h-[10px] border-[#ffffff5e] border-l-[0.1rem]" />
        <button className="text-white/80 hover:text-white flex items-center gap-1.5">
          <span>Privacy Policy</span>
        </button>
        <hr className="h-[10px] border-[#ffffff5e] border-l-[0.1rem]" />
        <button className="text-white/80 hover:text-white flex items-center gap-1.5">
          <IbiIcon icon="mdi:discord" className="w-4 h-4" />
        </button>
        <hr className="h-[10px] border-[#ffffff5e] border-l-[0.1rem]" />
        <button className="text-white/80 hover:text-white flex items-center gap-1.5">
          <IbiIcon icon="mdi:twitter" className="w-4 h-4" />
        </button>
      </div>

      <div className="flex items-center gap-3">
        <button className="text-white/80 hover:text-white flex items-center gap-1.5">
          <IbiIcon icon="mdi:ethereum" className="w-4 h-4" />
          <span>$ 2,516.59</span>
        </button>
        <hr className="h-[10px] border-[#ffffff5e] border-l-[0.1rem]" />
        <button className="text-white/80 hover:text-white flex items-center gap-1.5">
          <IbiIcon icon="mdi:gas" className="w-4 h-4" />
          <span>Gas</span>
        </button>
        <hr className="h-[10px] border-[#ffffff5e] border-l-[0.1rem]" />
        <button className="text-white/80 hover:text-white flex items-center gap-1.5">
          <IbiIcon icon="ic:round-question-answer" className="w-4 h-4" />
          <span>Support</span>
        </button>
        <hr className="h-[10px] border-[#ffffff5e] border-l-[0.1rem]" />
        <div className="flex items-center gap-1.5">
          <Tabs defaultValue="crypto" className="flex items-center bg-[#1B1D1F] rounded-md p-0.5">
            <TabsList className="bg-transparent px-1 h-[20px] py-0">
              <TabsTrigger
                value="crypto"
                className="text-xs px-1 py-0.5 mr-2 bg-transparent text-white data-[state=active]:bg-secondary-dark data-[state=active]:bg-opacity-70"
              >
                Crypto
              </TabsTrigger>
              <TabsTrigger
                value="usd"
                className="text-xs px-1 py-0.5 bg-transparent text-white data-[state=active]:bg-secondary-dark data-[state=active]:bg-opacity-70"
              >
                USD
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
        <hr className="h-[10px] border-[#ffffff5e] border-l-[0.1rem]" />
        <div className="flex items-center gap-1.5">
          <IbiIcon icon="ic:baseline-volume-up" className="w-4 h-4" />
        </div>
      </div>
    </div>
  );
};

export default MapFooterToolbar;
