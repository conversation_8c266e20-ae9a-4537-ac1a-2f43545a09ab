import useMap from "@/hooks/useMap";
import { ABC_WHITE_PLUS_REGULAR } from "@/utils/configs";
import { DEFAULT_MAP_ZOOM, MAPS_APIKEY } from "@/utils/constants";
import { Libraries, useJsApiLoader } from "@react-google-maps/api";
import { usePathname } from "next/navigation";
import React, { KeyboardEvent, useCallback, useMemo, useState } from "react";

import IbiIcon from "../IbiUi/IbiIcon";
import IbiInput from "../IbiUi/IbiInput";

const libraries: Libraries = ["places"];

interface PredictionStructuredFormatting {
  main_text: string;
  secondary_text: string;
}

interface Prediction {
  place_id: string;
  structured_formatting: PredictionStructuredFormatting;
}

const GoogleSearchPlace = () => {
  const { mapRef } = useMap();
  const pathname = usePathname();
  const [searchText, setSearchText] = useState("");
  const [predictions, setPredictions] = useState<Prediction[]>([]);
  const [showPredictions, setShowPredictions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);

  const isHydroMapActive = useMemo(() => pathname === "/predict", [pathname]);

  const { isLoaded, loadError } = useJsApiLoader({
    googleMapsApiKey: MAPS_APIKEY,
    libraries,
  });

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchText(value);
    setSelectedIndex(-1);

    if (value.length > 2 && window.google) {
      const service = new window.google.maps.places.AutocompleteService();
      service.getPlacePredictions({ input: value }, (predictions, status) => {
        if (status === window.google.maps.places.PlacesServiceStatus.OK && predictions) {
          setPredictions(predictions);
          setShowPredictions(true);
        }
      });
    } else {
      setPredictions([]);
      setShowPredictions(false);
    }
  }, []);

  const handleClearSearch = useCallback(() => {
    setSearchText("");
    setPredictions([]);
    setShowPredictions(false);
  }, []);

  const handlePlaceSelect = useCallback(
    (placeId: string) => {
      const service = new window.google.maps.places.PlacesService(document.createElement("div"));
      service.getDetails(
        {
          placeId,
          fields: ["formatted_address", "geometry"],
        },
        (place: google.maps.places.PlaceResult | null, status) => {
          if (
            status === window.google.maps.places.PlacesServiceStatus.OK &&
            place &&
            place.formatted_address &&
            place.geometry?.location
          ) {
            setSearchText(place.formatted_address);
            setShowPredictions(false);

            const newLng = place.geometry.location.lng();
            const newLat = place.geometry.location.lat();

            const flyToOptions = {
              center: [newLng, newLat],
              zoom: DEFAULT_MAP_ZOOM,
              essential: true,
              duration: 700,
            };

            const hydroMapInstance = isHydroMapActive ? (window as any).__hydroMapInstance : null;

            if (hydroMapInstance && typeof hydroMapInstance.flyTo === "function") {
              console.log("Using global HydroMap instance");
              try {
                hydroMapInstance.flyTo(flyToOptions);
                return;
              } catch (error) {
                console.error("Error using HydroMap instance:", error);
              }
            }

            if (mapRef?.current) {
              try {
                const mapInstance = mapRef.current as any;

                console.log("Map instance type:", mapInstance ? typeof mapInstance : "undefined");

                if (mapInstance.flyTo && typeof mapInstance.flyTo === "function") {
                  console.log("Using direct flyTo method");
                  mapInstance.flyTo(flyToOptions);
                } else if (mapInstance.getMap && typeof mapInstance.getMap === "function") {
                  console.log("Using getMap().flyTo method");
                  const actualMap = mapInstance.getMap();
                  if (actualMap && actualMap.flyTo) {
                    actualMap.flyTo(flyToOptions);
                  }
                } else {
                  console.log("Attempting alternative methods to access map");
                  if (mapInstance._map && mapInstance._map.flyTo) {
                    console.log("Using _map.flyTo method");
                    mapInstance._map.flyTo(flyToOptions);
                  } else {
                    console.log(
                      "Map access methods failed. Map instance structure:",
                      Object.keys(mapInstance).filter((k) => typeof mapInstance[k] !== "function"),
                    );
                  }
                }
              } catch (error) {
                console.error("Error navigating to location:", error);
              }
            } else {
              console.error("MapRef is not available");
            }
          }
        },
      );
    },
    [mapRef, isHydroMapActive],
  );

  const handleKeyDown = useCallback(
    (e: KeyboardEvent<HTMLInputElement>) => {
      if (!showPredictions) return;

      switch (e.key) {
        case "ArrowDown":
          e.preventDefault();
          setSelectedIndex((prev) => (prev < predictions.length - 1 ? prev + 1 : prev));
          break;
        case "ArrowUp":
          e.preventDefault();
          setSelectedIndex((prev) => (prev > 0 ? prev - 1 : -1));
          break;
        case "Enter":
          e.preventDefault();
          if (selectedIndex >= 0) {
            handlePlaceSelect(predictions[selectedIndex].place_id);
          }
          break;
        case "Escape":
          setShowPredictions(false);
          setSelectedIndex(-1);
          break;
      }
    },
    [showPredictions, predictions, selectedIndex, handlePlaceSelect],
  );

  if (loadError) {
    return <p style={ABC_WHITE_PLUS_REGULAR.style}>Failed to load</p>;
  }

  if (!isLoaded) {
    return <p>Loading search...</p>;
  }

  return (
    <div className="relative w-[180px] min-[1000px]:w-[420px] bg-black/85 backdrop-blur-xl shadow-lg rounded-md px-2 border border-[#ffffff1a]">
      <div className="relative">
        <IbiInput
          icon="iconamoon:search"
          id="search"
          placeholder="Search for a place worldwide"
          onChange={handleSearchChange}
          onKeyDown={handleKeyDown}
          value={searchText}
          onFocus={() => predictions.length > 0 && setShowPredictions(true)}
        />
        {searchText && (
          <button
            onClick={handleClearSearch}
            className="absolute right-1 mt-1 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white"
            aria-label="Clear search"
          >
            <IbiIcon icon="iconamoon:close" />
          </button>
        )}
      </div>

      {showPredictions && (
        <div className="relative">
          <div className="absolute z-40 w-[422px] -left-[1px] -top-[1px] mt-1 bg-[#ffffff1a] search-box-clip-path h-[302px]" />
          <div className="absolute z-50 w-[420px] mt-1 bg-primary-dark search-box-clip-path h-fit max-h-[300px] overflow-y-auto">
            {predictions.map((prediction, index) => (
              <div
                key={prediction.place_id}
                className={`py-3 pl-7 pr-3 cursor-pointer transition-colors ${
                  index === selectedIndex ? "bg-[#07121b]" : "hover:bg-[#07121b]"
                }`}
                onClick={() => handlePlaceSelect(prediction.place_id)}
              >
                <p className="font-medium text-gray-400">{prediction.structured_formatting.main_text}</p>
                <p className="text-sm text-gray-200">{prediction.structured_formatting.secondary_text}</p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default GoogleSearchPlace;
