import { Loader2 } from "lucide-react";

interface LoadingSpinnerProps {
  text?: string;
  size?: "sm" | "md" | "lg";
  variant?: "primary" | "secondary" | "white";
  className?: string;
  blocking?: boolean;
  direction?: "row" | "col";
}

const LoadingSpinner = ({
  text = "Loading...",
  size = "md",
  variant = "white",
  className = "",
  blocking = false,
  direction = "col",
}: LoadingSpinnerProps) => {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8",
  };

  const variantClasses = {
    primary: "text-primary",
    secondary: "text-neutral-600",
    white: "text-white",
  };

  if (blocking) {
    return (
      <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-xs">
        <div
          className={`"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 flex items-center gap-3 flex-${direction}`}
        >
          <Loader2 className={`animate-spin ${sizeClasses[size]} ${variantClasses[variant]}`} />
          {text && <span className={`text-sm font-medium ${variantClasses[variant]}`}>{text}</span>}
        </div>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-3 ${className} flex-${direction}`}>
      <Loader2 className={`animate-spin ${sizeClasses[size]} ${variantClasses[variant]}`} />
      {text && <span className={`text-sm font-medium ${variantClasses[variant]}`}>{text}</span>}
    </div>
  );
};

export default LoadingSpinner;
