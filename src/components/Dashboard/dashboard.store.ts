import { DashboardState } from "@/types";
import { createSelectors } from "@/utils/create-selectors";
import { create } from "zustand";

const useDashboardStore = create<DashboardState>((set, get) => ({
  searchQuery: "",
  currency: "USD",

  currencyData: [
    { code: "USD", name: "US Dollar", icon: "circle-flags:us" },
    { code: "ETH", name: "E<PERSON>", icon: "logos:ethereum" },
    { code: "BTC", name: "Bitcoin", icon: "cryptocurrency:btc" },
    { code: "EUR", name: "Euro", icon: "circle-flags:eu" },
    { code: "GBP", name: "British Pound", icon: "circle-flags:gb" },
  ],

  memeCoins: [
    {
      name: "747 Airlines (boeing)",
      price: 0.00000789,
      change: 8.5,
      image: "https://cdn.zerion.io/7ac933c0-fb6c-401a-a8de-600e66174a74.png",
    },
    {
      name: "<PERSON><PERSON>",
      price: 0.00000234,
      change: 5.2,
      image: "https://cdn.zerion.io/f460a759-3928-4b9b-81b2-ec80d85587e8.png",
    },
    {
      name: "Bonsai Token",
      price: 0.00000567,
      change: 7.1,
      image: "https://cdn.zerion.io/aedc6c00-609e-4097-a875-68778e54a91d.png",
    },
  ],

  stakingTokens: [
    {
      name: "Lido Staked ETH",
      price: 3245.67,
      change: 2.3,
      image: "https://cdn.zerion.io/******************************************.png",
    },
    {
      name: "Rocket Pool ETH",
      price: 3189.45,
      change: 1.8,
      image: "https://cdn.zerion.io/******************************************.png",
    },
    {
      name: "Coinbase Wrapped Staked ETH",
      price: 3298.12,
      change: 2.7,
      image: "https://cdn.zerion.io/******************************************.png",
    },
  ],

  cryptoIndexes: [
    {
      name: "DeFi Pulse",
      price: 127.99,
      change: -0.54,
      verified: true,
      image: "https://cdn.zerion.io/******************************************.png",
    },
    {
      name: "Metaverse Index",
      price: 39.8,
      change: 1,
      verified: true,
      image: "https://cdn.zerion.io/******************************************.png",
    },
    {
      name: "Diversified Staked ETH Index (dsETH)",
      price: 3722.05,
      change: -0.1,
      verified: true,
      image: "https://cdn.zerion.io/******************************************.png",
    },
  ],

  defiBlueChips: [
    {
      name: "Avalanche",
      price: 42.12,
      change: -2.5,
      verified: true,
      image: "https://cdn.zerion.io/43e05303-bf43-48df-be45-352d7567ff39.png",
    },
    {
      name: "Chainlink",
      price: 17.67,
      change: 0.32,
      verified: true,
      image: "https://cdn.zerion.io/******************************************.png",
    },
    {
      name: "Uniswap",
      price: 12.63,
      change: -3.7,
      verified: true,
      image: "https://cdn.zerion.io/******************************************.png",
    },
  ],

  trendingNFTs: [
    {
      name: "Bored Ape Yacht Club",
      price: 16.9,
      volume: "13K",
      volumeChange: 388.4,
      image:
        "https://lh3.googleusercontent.com/C_fjl1iM5iRwuk74N9DBrOmU-1-_lc_8x66BsWU8votTb3iwXiVJwmqJ2qd8BUI1DSDo_9KxcNcNJrdpnnxebLwpeJB7eiYSeI8",
    },
    {
      name: "Milady Maker",
      price: 6.4,
      volume: "256.06",
      volumeChange: 40.9,
      image:
        "https://lh3.googleusercontent.com/V7fVFUObJEFQjKKiDPnOmT_RuAsL63ZO3SBJgm2fXtt4ByOfmmTbPkNdgTTetyzxk8YmjT5CEQIjIBWAYmOEL6yMloxJBv_1FQ",
    },
  ],

  filteredMemeCoins: [],
  filteredStakingTokens: [],
  filteredCryptoIndexes: [],
  filteredDefiTokens: [],
  filteredNFTs: [],

  setSearchQuery: (query: string) => {
    const state = get();
    set({
      searchQuery: query,
      filteredMemeCoins: query
        ? state.memeCoins.filter((coin) => coin.name.toLowerCase().includes(query.toLowerCase()))
        : state.memeCoins,
      filteredStakingTokens: query
        ? state.stakingTokens.filter((token) => token.name.toLowerCase().includes(query.toLowerCase()))
        : state.stakingTokens,
      filteredCryptoIndexes: query
        ? state.cryptoIndexes.filter((index) => index.name.toLowerCase().includes(query.toLowerCase()))
        : state.cryptoIndexes,
      filteredDefiTokens: query
        ? state.defiBlueChips.filter((token) => token.name.toLowerCase().includes(query.toLowerCase()))
        : state.defiBlueChips,
      filteredNFTs: query
        ? state.trendingNFTs.filter((nft) => nft.name.toLowerCase().includes(query.toLowerCase()))
        : state.trendingNFTs,
    });
  },

  setCurrency: (currency: string) => set({ currency }),

  formatPrice: (price: number, curr?: string) => {
    const currency = curr || get().currency;
    switch (currency) {
      case "ETH":
        return `Ξ${price}`;
      case "BTC":
        return `₿${price}`;
      case "EUR":
        return `€${price}`;
      case "GBP":
        return `£${price}`;
      default:
        return `$${price}`;
    }
  },

  initialize: () => {
    const state = get();
    set({
      filteredMemeCoins: state.memeCoins,
      filteredStakingTokens: state.stakingTokens,
      filteredCryptoIndexes: state.cryptoIndexes,
      filteredDefiTokens: state.defiBlueChips,
      filteredNFTs: state.trendingNFTs,
    });
  },
}));

useDashboardStore.getState().initialize();

export const useDashboard = createSelectors(useDashboardStore);

export default useDashboardStore;
