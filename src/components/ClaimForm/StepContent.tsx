import HCaptcha from "@hcaptcha/react-hcaptcha";
import { motion } from "framer-motion";

import { FormData } from "./types";

interface StepContentProps {
  formData: FormData;
  setFormData: (data: FormData) => void;
  onCaptchaChange: (token: string | null) => void;
}

const StepContent = ({ formData, setFormData, onCaptchaChange }: StepContentProps) => {
  const motionProps = {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 },
  };

  return (
    <motion.div {...motionProps} className="space-y-6">
      <div className="space-y-2">
        <label className="text-sm font-medium text-neutral-300">
          Email Address <span className="text-red-500">*</span>
        </label>
        <input
          type="email"
          value={formData.email}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          className="w-full rounded-md border border-neutral-800 bg-neutral-900 px-3 py-2 text-sm text-neutral-100 focus:border-blue-600 focus:ring-1 focus:ring-blue-600 transition-colors"
          placeholder="Enter your email address"
        />
      </div>

      <div className="space-y-3 pt-4">
        {["contactConsent", "ownershipConsent"].map((consent) => (
          <label key={consent} className="flex items-start gap-2">
            <input
              type="checkbox"
              checked={formData[consent as keyof FormData] as boolean}
              onChange={(e) => setFormData({ ...formData, [consent]: e.target.checked })}
              className="mt-1"
            />
            <span className="text-sm text-neutral-300">
              {consent === "contactConsent"
                ? "I agree to be contacted by Ibicash team to verify ownership"
                : "I agree I am the owner or representative of the title holder"}
              <span className="text-red-500">*</span>
            </span>
          </label>
        ))}
      </div>

      <div className="pt-4">
        <HCaptcha sitekey={"10000000-ffff-ffff-ffff-000000000001"} onVerify={onCaptchaChange} theme="dark" />
      </div>
    </motion.div>
  );
};

export default StepContent;
