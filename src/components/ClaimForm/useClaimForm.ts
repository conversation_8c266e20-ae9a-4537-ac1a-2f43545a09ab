import { useToast } from "@/hooks/useToast";
import { useClaimsMutations } from "@/queries/mutations/claim.mutations";
import { useState } from "react";

import { type FormData } from "./types";

const INITIAL_FORM_DATA: FormData = {
  email: "",
  captchaToken: "",
  contactConsent: false,
  ownershipConsent: false,
};

export const useClaimForm = (
  propertyId: string,
  userEmail?: string,
  isClaimed: boolean = false,
  claimId: string | null = null,
) => {
  const { requestOwnership, challengeClaim } = useClaimsMutations();
  const { toast } = useToast();
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    ...INITIAL_FORM_DATA,
    email: userEmail || "",
  });

  const resetForm = () => {
    setFormData({ ...INITIAL_FORM_DATA, email: userEmail || "" });
  };

  const handleModalChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) resetForm();
  };

  const isSubmitButtonDisabled = () =>
    requestOwnership.isPending ||
    !formData.email ||
    !formData.captchaToken ||
    !formData.contactConsent ||
    !formData.ownershipConsent;

  const validateForm = () => {
    const validations = [
      { condition: !formData.email, message: "Please enter your email address" },
      { condition: !formData.captchaToken, message: "Please complete the captcha verification" },
      {
        condition: !formData.contactConsent || !formData.ownershipConsent,
        message: "Please agree to all required terms",
      },
    ];

    const failed = validations.find((v) => v.condition);
    if (failed) {
      toast({
        title: "Missing Information",
        description: failed.message,
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    const formDataToSend = new FormData();
    formDataToSend.append("email", formData.email);
    formDataToSend.append("captchaToken", formData.captchaToken || "");
    formDataToSend.append("propertyId", propertyId);
    formDataToSend.append("contactConsent", formData.contactConsent ? "true" : "false");
    formDataToSend.append("ownershipConsent", formData.ownershipConsent ? "true" : "false");

    try {
      if (isClaimed && claimId) {
        formDataToSend.append("claimId", claimId);
        await challengeClaim.mutateAsync(formDataToSend);
        toast({
          title: "Challenge Submitted Successfully",
          description: "We'll review your challenge and get back to you soon.",
        });
      } else {
        await requestOwnership.mutateAsync(formDataToSend);
        toast({
          title: "Claim Submitted Successfully",
          description: "We'll review your claim and get back to you soon.",
        });
      }
      setOpen(false);
    } catch (error) {
      toast({
        title: "Submission Failed",
        description: "There was an error submitting your claim. Please try again.",
        variant: "destructive",
      });
    }
  };

  return {
    open,
    formData,
    setFormData,
    handleModalChange,
    handleSubmit,
    isSubmitButtonDisabled,
    requestOwnership,
  };
};
