import usePrivyAuth from "@/hooks/usePrivyAuth";
import { cn } from "@/lib/utils";
import { useEffect, useRef } from "react";

import StepContent from "./StepContent";
import { ClaimFormProps } from "./types";
import { useClaimForm } from "./useClaimForm";

const getUserEmail = (user: any): string | undefined => {
  if (!user) return undefined;

  if (user.email?.address) return user.email.address;

  if (user.linkedAccounts?.length) {
    const emailAccount = user.linkedAccounts.find((account: any) => account.email);
    if (emailAccount?.email) return emailAccount.email;
  }

  if (user.google?.email) return user.google.email;

  return undefined;
};

const ClaimForm = ({
  children,
  propertyId,
  isClaimed,
  claimId,
}: ClaimFormProps & { isClaimed: boolean; claimId: string | null }) => {
  const { user } = usePrivyAuth();
  const userEmail = getUserEmail(user);
  const modalRef = useRef<HTMLDivElement>(null);

  const { open, formData, setFormData, handleModalChange, handleSubmit, isSubmitButtonDisabled, requestOwnership } =
    useClaimForm(propertyId, userEmail, isClaimed, claimId);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        handleModalChange(false);
      }
    };

    if (open) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [open, handleModalChange]);

  return (
    <>
      <div onClick={() => handleModalChange(true)}>
        {children || <button className="w-full">Claim Property</button>}
      </div>

      {open && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 50,
        }}>
          <div
            ref={modalRef}
            style={{
              width: '100%',
              maxWidth: '700px',
              backgroundColor: '#030303',
              border: '1px solid #262626',
              borderRadius: '0.375rem',
              padding: '1.5rem',
              position: 'relative',
            }}
          >
            <div style={{ marginBottom: '1rem' }}>
              <h2 style={{ color: '#f5f5f5', fontSize: '1.25rem', fontWeight: 'bold' }}>Claim Property</h2>
              <button
                onClick={() => handleModalChange(false)}
                style={{
                  position: 'absolute',
                  top: '1rem',
                  right: '1rem',
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  color: '#a3a3a3',
                  fontSize: '1.5rem',
                }}
              >
                ×
              </button>
            </div>

            <div className="py-4">
              <div className="flex justify-center mb-8">
                <div className="flex items-center">
                  <div className="text-sm text-neutral-400">Insert an email to claim the property</div>
                </div>
              </div>

              <StepContent
                formData={formData}
                setFormData={setFormData}
                onCaptchaChange={(token) => setFormData((prev) => ({ ...prev, captchaToken: token || "" }))}
              />

              <div className="flex justify-end mt-8">
                <button
                  onClick={handleSubmit}
                  disabled={isSubmitButtonDisabled()}
                  className={cn(
                    "w-24 text-white",
                    requestOwnership.isPending ? "bg-blue-600/50 cursor-not-allowed" : "bg-blue-600 hover:bg-blue-700",
                  )}
                >
                  {requestOwnership.isPending ? (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                      <span>Sending</span>
                    </div>
                  ) : (
                    "Submit"
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ClaimForm;