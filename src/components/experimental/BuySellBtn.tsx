import { TrendingDown, TrendingUp } from "lucide-react";
import React, { useEffect, useRef } from "react";

const FuturisticButtons = () => {
  const buyRef = useRef<HTMLButtonElement>(null);
  const sellRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    const buyButton = buyRef.current;
    const sellButton = sellRef.current;

    if (!buyButton || !sellButton) return;

    buyButton.addEventListener("mouseenter", () => {
      buyButton.classList.add("scale-105");
    });

    buyButton.addEventListener("mouseleave", () => {
      buyButton.classList.remove("scale-105");
    });

    sellButton.addEventListener("mouseenter", () => {
      sellButton.classList.add("scale-105");
    });

    sellButton.addEventListener("mouseleave", () => {
      sellButton.classList.remove("scale-105");
    });

    return () => {
      if (buyButton) {
        buyButton.removeEventListener("mouseenter", () => {});
        buyButton.removeEventListener("mouseleave", () => {});
      }
      if (sellButton) {
        sellButton.removeEventListener("mouseenter", () => {});
        sellButton.removeEventListener("mouseleave", () => {});
      }
    };
  }, []);

  return (
    <div className="flex items-center justify-center space-x-3">
      <button
        ref={buyRef}
        className="relative flex items-center justify-center py-2 px-6 rounded bg-black border border-emerald-500 text-emerald-400 text-sm font-medium overflow-hidden transition-transform duration-300 shadow-lg"
      >
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute inset-0 opacity-30 bg-gradient-to-r from-transparent via-emerald-500 to-transparent animate-scan" />
        </div>

        <div className="absolute top-0 left-0 w-2 h-2 border-t border-l border-emerald-400" />
        <div className="absolute top-0 right-0 w-2 h-2 border-t border-r border-emerald-400" />
        <div className="absolute bottom-0 left-0 w-2 h-2 border-b border-l border-emerald-400" />
        <div className="absolute bottom-0 right-0 w-2 h-2 border-b border-r border-emerald-400" />

        <div className="absolute left-0 top-0 w-full h-0.5">
          <div className="h-full bg-emerald-500 opacity-70 animate-activity-line" />
        </div>
        <div className="absolute right-0 top-0 w-0.5 h-full">
          <div className="w-full bg-emerald-500 opacity-70 animate-activity-line-vertical" />
        </div>

        <div className="flex items-center justify-center gap-2 z-10">
          <TrendingUp size={14} className="animate-pulse" />
          <span className="font-extrabold">Buy shares</span>
        </div>
      </button>

      <button
        ref={sellRef}
        className="relative flex items-center justify-center py-2 px-6 rounded bg-black border border-rose-500 text-rose-400 text-sm font-medium overflow-hidden transition-transform duration-300 shadow-lg"
      >
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute inset-0 opacity-30 bg-gradient-to-r from-transparent via-rose-500 to-transparent animate-scan" />
        </div>

        <div className="absolute top-0 left-0 w-2 h-2 border-t border-l border-rose-400" />
        <div className="absolute top-0 right-0 w-2 h-2 border-t border-r border-rose-400" />
        <div className="absolute bottom-0 left-0 w-2 h-2 border-b border-l border-rose-400" />
        <div className="absolute bottom-0 right-0 w-2 h-2 border-b border-r border-rose-400" />

        <div className="absolute left-0 top-0 w-full h-0.5">
          <div className="h-full bg-rose-500 opacity-70 animate-activity-line-reverse" />
        </div>
        <div className="absolute right-0 top-0 w-0.5 h-full">
          <div className="w-full bg-rose-500 opacity-70 animate-activity-line-vertical-reverse" />
        </div>

        <div className="flex items-center justify-center gap-2 z-10">
          <TrendingDown size={14} className="animate-pulse" />
          <span className="font-extrabold">Sell shares</span>
        </div>
      </button>

      <style jsx>{`
        @keyframes scan {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(100%);
          }
        }
        @keyframes activity-line {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(100%);
          }
        }
        @keyframes activity-line-reverse {
          0% {
            transform: translateX(100%);
          }
          100% {
            transform: translateX(-100%);
          }
        }
        @keyframes activity-line-vertical {
          0% {
            transform: translateY(-100%);
          }
          100% {
            transform: translateY(100%);
          }
        }
        @keyframes activity-line-vertical-reverse {
          0% {
            transform: translateY(100%);
          }
          100% {
            transform: translateY(-100%);
          }
        }
        .animate-scan {
          animation: scan 3s infinite linear;
        }
        .animate-activity-line {
          animation: activity-line 2s infinite linear;
        }
        .animate-activity-line-reverse {
          animation: activity-line-reverse 2s infinite linear;
        }
        .animate-activity-line-vertical {
          animation: activity-line-vertical 3s infinite linear;
        }
        .animate-activity-line-vertical-reverse {
          animation: activity-line-vertical-reverse 3s infinite linear;
        }
      `}</style>
    </div>
  );
};

export default FuturisticButtons;
