import React, { useState } from "react";
import { Area, Area<PERSON>hart, ResponsiveContainer, Tooltip, <PERSON>Axis, YAxis } from "recharts";

import BuySellBtn from "../BuySellBtn";

interface VoronoiTradingWidgetProps {
  voronoiId?: string;
  testMode?: boolean;
}

interface PriceData {
  buyPrice: string;
  sellPrice: string;
  totalSupply: string;
  dailyVolume: string;
  priceChange24h: {
    value: string;
    percentage: number;
  };
}

const MOCK_DATA: PriceData = {
  buyPrice: "0.0025",
  sellPrice: "0.0022",
  totalSupply: "15780",
  dailyVolume: "1.42",
  priceChange24h: {
    value: "+0.0003",
    percentage: 12.5,
  },
};

const MOCK_CHART_DATA = [
  { time: "00:00", price: 0.0022 },
  { time: "04:00", price: 0.0023 },
  { time: "08:00", price: 0.0024 },
  { time: "12:00", price: 0.0021 },
  { time: "16:00", price: 0.0023 },
  { time: "20:00", price: 0.0025 },
  { time: "24:00", price: 0.0026 },
];

const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-[#1A1A1A] p-2 rounded-md border border-gray-700">
        <p className="text-gray-200">{`${payload[0].value.toFixed(6)} ETH`}</p>
        <p className="text-gray-500 text-xs">{payload[0].payload.time}</p>
      </div>
    );
  }
  return null;
};

const VoronoiTradingWidget: React.FC<VoronoiTradingWidgetProps> = ({ voronoiId = "1235" }) => {
  const [timeframe, setTimeframe] = useState<"1D" | "1W" | "1M" | "1Y">("1D");
  const [amount, setAmount] = useState("1.0");
  const [isLoading] = useState(false);

  const formatPercentage = (percentage: number) => {
    const sign = percentage >= 0 ? "+" : "";
    return `${sign}${percentage.toFixed(2)}%`;
  };

  const formatPrice = (price: string) => {
    const numPrice = parseFloat(price);
    return numPrice.toLocaleString("en-US", {
      minimumFractionDigits: 6,
      maximumFractionDigits: 6,
    });
  };

  return (
    <div className="w-full bg-gray-950 rounded-xl border border-gray-800 overflow-hidden">
      <div className="p-4 border-b border-gray-800">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-medium text-white">Voronoi Cell #{voronoiId}</h2>
          <div className="text-gray-400 text-sm">
            {isLoading ? "Loading..." : `${MOCK_DATA.totalSupply} shares in circulation`}
          </div>
        </div>
      </div>

      <div className="p-4 border-b border-gray-800">
        <div className="flex justify-between">
          <div>
            <div className="text-gray-400 text-sm">Current Price (ETH)</div>
            <div className="text-2xl font-semibold text-white mt-1">
              {isLoading ? "..." : formatPrice(MOCK_DATA.buyPrice)}
            </div>
            <div
              className={`text-sm mt-1 ${MOCK_DATA.priceChange24h.percentage >= 0 ? "text-green-400" : "text-red-400"}`}
            >
              {isLoading ? "..." : `${formatPercentage(MOCK_DATA.priceChange24h.percentage)} (24h)`}
            </div>
          </div>
          <div className="text-right">
            <div className="text-gray-400 text-sm">Daily Volume (ETH)</div>
            <div className="text-xl font-medium text-white mt-1">{isLoading ? "..." : MOCK_DATA.dailyVolume}</div>
          </div>
        </div>

        <div className="mt-4 h-36">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={MOCK_CHART_DATA} margin={{ top: 5, right: 5, bottom: 5, left: 5 }}>
              <defs>
                <linearGradient id="colorPrice" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#10B981" stopOpacity={0.3} />
                  <stop offset="95%" stopColor="#10B981" stopOpacity={0} />
                </linearGradient>
              </defs>
              <XAxis
                dataKey="time"
                axisLine={false}
                tickLine={false}
                tick={{ fill: "#666", fontSize: 10 }}
                stroke="#666666"
                minTickGap={20}
              />
              <YAxis domain={["dataMin - 0.0001", "dataMax + 0.0001"]} hide />
              <Tooltip content={<CustomTooltip />} cursor={{ fill: "transparent" }} />
              <Area
                type="monotone"
                dataKey="price"
                stroke="#10B981"
                strokeWidth={2}
                fillOpacity={1}
                fill="url(#colorPrice)"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        <div className="flex mt-4 space-x-2">
          {["1D", "1W", "1M", "1Y"].map((tf) => (
            <button
              key={tf}
              className={`px-3 py-1 rounded-md text-sm ${
                timeframe === tf
                  ? "bg-green-500 bg-opacity-20 text-green-400"
                  : "bg-gray-800 text-gray-400 hover:bg-gray-700"
              }`}
              onClick={() => setTimeframe(tf as any)}
            >
              {tf}
            </button>
          ))}
        </div>
      </div>

      <div className="p-4">
        <div className="mb-4">
          <label htmlFor="amount" className="block text-sm text-gray-400 mb-1">
            Amount (shares)
          </label>
          <input
            id="amount"
            type="text"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            className="w-full bg-gray-800 border border-gray-700 rounded-md py-2 px-3 text-white"
            placeholder="Enter amount"
          />
        </div>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="bg-gray-800 rounded-lg p-4">
            <div className="text-gray-400 text-sm">Buy Price</div>
            <div className="text-xl font-medium text-white mt-1">
              {isLoading ? "..." : formatPrice(MOCK_DATA.buyPrice)} ETH
            </div>
          </div>
          <div className="bg-gray-800 rounded-lg p-4">
            <div className="text-gray-400 text-sm">Sell Price</div>
            <div className="text-xl font-medium text-white mt-1">
              {isLoading ? "..." : formatPrice(MOCK_DATA.sellPrice)} ETH
            </div>
          </div>
        </div>

        <BuySellBtn />

        <div className="mt-4 text-sm text-gray-400">
          <p>Prices are shown per share. Actual prices may vary based on transaction size.</p>
        </div>
      </div>
    </div>
  );
};

export default VoronoiTradingWidget;
