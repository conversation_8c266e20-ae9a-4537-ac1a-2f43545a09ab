import { toast } from "@/hooks/useToast";
import { useCollectionsMutations } from "@/queries/mutations/collections.mutations";
import { territoriesService } from "@/services/territories.service";
import { handleStorage } from "@/utils/storage";
import { useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface PropertyDetailsModalProps {
  propertyId: string | null;
  collectionId?: string | null;
  onClose: () => void;
}

const PropertyDetailsModal = ({ propertyId, onClose, collectionId }: PropertyDetailsModalProps) => {
  const [polygonImageUrl, setPolygonImageUrl] = useState<string>("");
  const [imageError, setImageError] = useState(false);
  const [isLoadingImage, setIsLoadingImage] = useState(false);
  const { data, isLoading, error } = useQuery({
    queryKey: ["property-details", propertyId],

    // TODO: passed 0 to prevent build fail, fix it later.
    // build test
    queryFn: () => territoriesService.getPropertyDetails(propertyId as string, 0, 0),
    enabled: !!propertyId,
  });
  const router = useRouter();
  const { unbookmarkProperty } = useCollectionsMutations();

  const handleMapNavigation = async () => {
    if (data?.ibiCode) {
      handleStorage("session", "ibiCode", "create", data.ibiCode);
      router.push("/claim");
    }
  };

  const handleSaveBookmark = () => {
    unbookmarkProperty.mutate(
      { id: collectionId as string, propertyId: propertyId as string },
      {
        onSuccess: () => {
          toast({
            title: "Bookmark Removed",
            description: "Your bookmark has been removed from the selected collection.",
          });
          onClose();
        },
        onError: () => {
          toast({
            title: "Error",
            description: "An error occurred while removing the bookmark.",
          });
        },
      },
    );
  };

  const loadPolygonImage = async (ibicode: string) => {
    if (!ibicode) return;

    setIsLoadingImage(true);
    try {
      const baseUrl = process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : "http://localhost:3000";
      const response = await fetch(`${baseUrl}/api/map-info/${ibicode}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const encodedGeojson = encodeURIComponent(JSON.stringify(data.geojson));
      const mapboxUrl = `https://api.mapbox.com/styles/v1/geodatin/clawpmxqa000014mrgrn39mtd/static/geojson(${encodedGeojson})/auto/600x400?access_token=${process.env.MAPBOX_TOKEN}`;

      setImageError(false);
      setPolygonImageUrl(mapboxUrl);
    } catch (error) {
      console.error("Error loading polygon image:", error);
      setPolygonImageUrl("");
      setImageError(true);
    } finally {
      setIsLoadingImage(false);
    }
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const resetState = () => {
    setPolygonImageUrl("");
    setImageError(false);
    setIsLoadingImage(false);
    onClose();
  };

  useEffect(() => {
    if (data?.ibiCode) {
      loadPolygonImage(data.ibiCode);
    }
  }, [data?.ibiCode]);

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        display: propertyId ? 'flex' : 'none',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000
      }}
      onClick={resetState}
    >
      <div
        style={{
          backgroundColor: 'rgba(1, 3, 3, 0.95)',
          border: '1px solid rgb(31, 41, 55)',
          color: 'white',
          maxWidth: '36rem',
          width: '90%',
          maxHeight: '90vh',
          overflowY: 'auto',
          borderRadius: '8px',
          padding: '24px'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {isLoading ? (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div style={{ width: "100%", height: "24px", backgroundColor: "#2c2f33", borderRadius: "4px" }} />
            <div style={{ width: "100%", height: "300px", backgroundColor: "#2c2f33", borderRadius: "8px" }} />
          </div>
        ) : error ? (
          <div style={{ textAlign: 'center', padding: '32px 0' }}>
            <p style={{ color: 'rgb(156, 163, 175)' }}>Failed to load property details</p>
          </div>
        ) : (
          <>
            <div style={{ marginBottom: '24px' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
                <h2 style={{ fontSize: '20px', fontWeight: 'bold' }}>#{data?.ibiCode}</h2>
                <span style={{ fontSize: '14px', color: 'rgb(156, 163, 175)' }}>ID: {data?.id}</span>
              </div>
              <div style={{ display: 'flex', gap: '16px', fontSize: '14px', color: 'rgb(156, 163, 175)' }}>
                <div>
                  <span style={{ fontWeight: '500' }}>Total Area:</span> {Number(data?.areaHa).toFixed(2)} ha
                </div>
                <div>
                  <span style={{ fontWeight: '500' }}>Forest Area:</span> {data?.totalForestArea.toFixed(2)} ha
                </div>
              </div>
            </div>

            {isLoadingImage ? (
              <div style={{ width: '100%', height: '300px', backgroundColor: 'rgb(31, 41, 55)', borderRadius: '8px', animation: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite' }} />
            ) : (
              <img
                src={
                  imageError ? "https://cdn.rohde-schwarz.com/pws/_tech/images/map-placeholder.png" : polygonImageUrl
                }
                alt="Property polygon"
                style={{ width: '100%', height: 'fit-content', objectFit: 'cover', borderRadius: '8px' }}
                onError={handleImageError}
              />
            )}

            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px', marginTop: '16px' }}>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <h3 style={{ fontWeight: '500', fontSize: '18px' }}>Ecoregions</h3>
                <span style={{ fontSize: '14px', color: 'rgb(156, 163, 175)' }}>
                  {data?.propertyEcoregions.length} region{data?.propertyEcoregions.length !== 1 ? "s" : ""}
                </span>
              </div>
              <div style={{ display: 'grid', gap: '12px' }}>
                {data?.propertyEcoregions.map((item) => (
                  <div key={item.ecoregion.code} style={{ padding: '16px', borderRadius: '8px', backgroundColor: 'rgba(10, 14, 21, 1)', border: '1px solid rgba(31, 41, 55, 0.3)' }}>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                        <h4 style={{ fontWeight: '500' }}>{item.ecoregion.name}</h4>
                        <span style={{ fontSize: '14px', color: 'rgb(156, 163, 175)' }}>{item.forestArea.toFixed(2)} ha</span>
                      </div>
                      <p style={{ fontSize: '14px', color: 'rgb(156, 163, 175)' }}>
                        <span style={{ fontWeight: '500' }}>Biome:</span> {item.ecoregion.biomeName}
                      </p>
                      <p style={{ fontSize: '12px', color: 'rgb(107, 114, 128)' }}>Code: {item.ecoregion.code}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'flex-end', gap: '8px', marginTop: '16px' }}>
              {collectionId && (
                <button onClick={handleSaveBookmark}>
                  Remove Bookmark
                </button>
              )}
              <button onClick={handleMapNavigation}>
                Navigate to property
              </button>
            </div>
          </>
        )}
      </div>
    </div >
  );
};

export default PropertyDetailsModal;
