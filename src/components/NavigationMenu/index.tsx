import { cn } from "@/lib/utils";
import { navigationOptions } from "@/utils/constants";
import { usePathname, useRouter } from "next/navigation";

import IbiIcon from "../IbiUi/IbiIcon";

const NavigationMenu = ({ open }: { open: boolean }) => {
  const pathname = usePathname();
  const router = useRouter();

  return (
    <div className="w-full flex flex-col justify-between h-full">
      <div className="h-[108px] flex">
        <div className="w-[50px] ml-0.5 flex mt-4 justify-center">
          <img
            src="https://ik.imagekit.io/97q72hphb/Union.png?updatedAt=1744141959581"
            alt="logo"
            className="w-[30px] h-[27.22px] cursor-pointer"
            onClick={() => router.push("/claim")}
          />
        </div>
      </div>

      <div className="flex-1 flex flex-col justify-between pb-4">
        <div className="flex flex-col gap-4">
          {navigationOptions.map((option) => (
            <div key={option.pathname} className="relative">
              <div role="button" className="flex items-center h-8 group" onClick={() => router.push(option.pathname)}>
                <div className="w-[55px] flex justify-center">
                  <IbiIcon
                    icon={option.icon}
                    className={cn(
                      "text-2xl transition-opacity",
                      pathname === option.pathname ? "opacity-100" : "opacity-50 group-hover:opacity-100",
                    )}
                  />
                </div>

                <div
                  className={cn(
                    "overflow-hidden transition-all duration-500 ease-in-out",
                    open ? "w-[130px] opacity-100" : "w-0 opacity-0",
                  )}
                >
                  <span
                    className={cn(
                      "text-sm font-semibold whitespace-nowrap",
                      pathname === option.pathname ? "text-white" : "text-gray-400 group-hover:text-white",
                    )}
                  >
                    {option.label}
                  </span>
                </div>
              </div>

              {open && option.subpaths && (pathname === "/explore" || pathname?.startsWith("/explore/")) && (
                <div className="opacity-100">
                  <div className={cn("flex flex-col gap-1", !open && "hidden")}>
                    {option.subpaths.map((subpath) => (
                      <button
                        key={subpath.pathname}
                        className="flex items-center h-4 pl-[55px] mt-2"
                        onClick={() => router.push(subpath.pathname)}
                      >
                        <IbiIcon icon={subpath.icon} className="text-xs shrink-0" />
                        <span
                          className={cn(
                            "ml-2 text-sm font-medium whitespace-nowrap",
                            pathname === subpath.pathname ? "text-white" : "text-gray-400 hover:text-white",
                          )}
                        >
                          {subpath.label}
                        </span>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        <div>
          <div role="button" className="flex items-center h-8" onClick={() => router.push("/settings")}>
            <div className="w-[55px] flex justify-center">
              <IbiIcon
                icon="fluent:settings-28-regular"
                className={cn(
                  "text-2xl transition-opacity",
                  pathname === "/settings" ? "opacity-100" : "opacity-50 hover:opacity-100",
                )}
              />
            </div>
            <div
              className={cn(
                "overflow-hidden transition-all duration-500 ease-in-out",
                open ? "w-[130px] opacity-100" : "w-0 opacity-0",
              )}
            >
              <span
                className={cn(
                  "text-sm font-semibold whitespace-nowrap",
                  pathname === "/settings" ? "text-white" : "text-gray-400 hover:text-white",
                )}
              >
                Settings
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NavigationMenu;
