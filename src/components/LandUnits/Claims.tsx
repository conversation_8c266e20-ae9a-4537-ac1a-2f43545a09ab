"use client";

import PropertyDetailsModal from "@/components/Bookmark/PropertyDetailsModal";
import { StyledCard } from "../ui/StyledFallbacks";
import { useClaims } from "@/queries/hooks/useClaims";
import { useEffect, useState } from "react";

interface ClaimCardProps {
  claim: {
    id: string;
    propertyId: string;
    status: string;
    createdAt: string;
    documents: Array<{
      claimId: string;
      documentId: string;
      document: {
        id: string;
        name: string;
        url: string;
      };
    }>;
  };
  onViewDetails: (id: string) => void;
}

const ClaimCard = ({ claim, onViewDetails }: ClaimCardProps) => {
  const [imageUrl, setImageUrl] = useState<string>("");
  const [imageError, setImageError] = useState(false);
  const [isLoadingImage, setIsLoadingImage] = useState(false);

  const loadPolygonImage = async (propertyId: string) => {
    if (!propertyId) return;

    setIsLoadingImage(true);
    try {
      const baseUrl = process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : "http://localhost:3000";
      const response = await fetch(`${baseUrl}/api/map-info/${propertyId}?isProperty=true`);

      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

      const data = await response.json();
      const encodedGeojson = encodeURIComponent(JSON.stringify(data.geojson));
      const mapboxUrl = `https://api.mapbox.com/styles/v1/geodatin/clawpmxqa000014mrgrn39mtd/static/geojson(${encodedGeojson})/auto/600x400?access_token=${process.env.MAPBOX_TOKEN}`;

      setImageError(false);
      setImageUrl(mapboxUrl);
    } catch (error) {
      console.error("Error loading polygon image:", error);
      setImageError(true);
    } finally {
      setIsLoadingImage(false);
    }
  };

  useEffect(() => {
    if (claim.id) {
      loadPolygonImage(claim.propertyId);
    }
  }, [claim.id]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <StyledCard className="bg-[#010303]/70 border-gray-800 overflow-hidden hover:border-gray-700 transition-colors">
      <div className="relative h-48 overflow-hidden">
        {isLoadingImage ? (
          <div className="w-full h-full bg-gray-800 animate-pulse" />
        ) : (
          <img
            src={imageError ? "https://cdn.rohde-schwarz.com/pws/_tech/images/map-placeholder.png" : imageUrl}
            alt={`Property`}
            className="w-full h-full object-cover"
            onError={() => setImageError(true)}
          />
        )}
      </div>
      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-bold text-white">#</h3>
          <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-yellow-500/10 text-yellow-500">
            <span className="w-1.5 h-1.5 rounded-full bg-yellow-500" />
            {claim.status || "Unknown"}
          </span>
        </div>
        <div className="space-y-2 text-sm text-gray-400">
          <p>Created: {formatDate(claim.createdAt)}</p>
          <p>Documents: {claim.documents ? claim.documents.length : 0}</p>
        </div>
        <button className="w-full mt-4" onClick={() => onViewDetails(claim.propertyId)}>
          View Details
        </button>
      </div>
    </StyledCard>
  );
};

export function LandUnitsClaims() {
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedPropertyId, setSelectedPropertyId] = useState<string | null>(null);
  const { claims } = useClaims(currentPage);

  const claimList = claims?.data?.data?.data || [];
  const meta = claims?.data?.data?.meta;

  if (claims.isLoading) {
    return (
      <div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
      >
        {[...Array(6)].map((_, i) => (
          <StyledCard key={i} className="bg-[#010303]/70 border-gray-800">
            <div className="h-48 bg-gray-800 animate-pulse" />
            <div className="p-4 space-y-4">
              <div style={{ height: "20px", width: "100%", backgroundColor: "#2d3748" }} className="animate-pulse" />
              <div style={{ height: "16px", width: "75%", backgroundColor: "#2d3748" }} className="animate-pulse" />
              <div style={{ height: "36px", width: "100%", backgroundColor: "#2d3748" }} className="animate-pulse" />
            </div>
          </StyledCard>
        ))}
      </div>
    );
  }

  if (claims.isError) {
    return <div className="text-red-500 text-lg">Error loading claims. Please try again later.</div>;
  }

  return (
    <>
      <div className="space-y-8">
        {claimList.length ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {claimList.map((claim) => (
                <ClaimCard key={claim.id} claim={claim} onViewDetails={setSelectedPropertyId} />
              ))}
            </div>

            {meta && (
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-400">
                  Page {meta.page} of {meta.pageCount}
                </div>
                <div className="flex gap-2">
                  <button

                    onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                    disabled={!meta.hasPreviousPage}
                  >
                    Previous
                  </button>
                  <button onClick={() => setCurrentPage((p) => p + 1)} disabled={!meta.hasNextPage}>
                    Next
                  </button>
                </div>
              </div>
            )}
          </>
        ) : (
          <StyledCard className="bg-[#010303]/70 border-gray-800">
            <div className="flex flex-col items-center justify-center py-12">
              <div className="p-4 bg-blue-500/10 rounded-full mb-4">
                icon
              </div>
              <h3 className="text-xl font-medium text-white mb-2">No Claims Yet</h3>
              <p className="text-gray-400 mb-6">Start by creating your first property claim!</p>
              <a href="/claim">
                Create Claim
              </a>
            </div>
          </StyledCard>
        )}
      </div>

      <PropertyDetailsModal propertyId={selectedPropertyId} onClose={() => setSelectedPropertyId(null)} />
    </>
  );
}
