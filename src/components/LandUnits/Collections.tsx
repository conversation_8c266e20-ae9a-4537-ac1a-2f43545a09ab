"use client";

import PropertyDetailsModal from "@/components/Bookmark/PropertyDetailsModal";
import { useCollections } from "@/queries/hooks/useCollections";
import { useState } from "react";
import { StyledCard } from "../ui/StyledFallbacks";

function CollectionsSkeleton() {
  return (
    <div className="space-y-4">
      {[1, 2, 3].map((i) => (
        <StyledCard key={i} className="bg-[#010303]/70 border-gray-800">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-500/20 rounded-lg w-10 h-10 animate-pulse" />
            <div className="space-y-2">
              <div className="h-5 w-32 bg-gray-700 rounded animate-pulse" />
              <div className="h-4 w-24 bg-gray-700/50 rounded animate-pulse" />
            </div>
          </div>
        </StyledCard>
      ))}
    </div>
  );
}

function CollectionsError() {
  return (
    <StyledCard className="bg-red-500/10 border-red-500/30">
      <div className="flex items-center gap-4">
        <div className="p-3 bg-red-500/20 rounded-full">
          icon
        </div>
        <div>
          <h3 className="font-medium text-red-400">Failed to load collections</h3>
          <p className="text-sm text-red-300/70">Please try refreshing the page or try again later.</p>
        </div>
      </div>
    </StyledCard>
  );
}

function EmptyCollections() {
  return (
    <StyledCard className="bg-[#010303]/70 border-gray-800">
      <div className="p-2 text-center">
        <div className="flex flex-col items-center gap-4">
          <div className="p-4 bg-blue-500/10 rounded-full">
            icon
          </div>
          <div>
            <h3 className="font-medium text-white text-lg">No Collections Found</h3>
            <p className="text-sm text-gray-400 mt-1">Start by creating your first collection</p>
          </div>
          <a className="mt-4" href="/claim">
            Create Collection
          </a>
        </div>
      </div >
    </StyledCard >
  );
}

export function LandUnitsCollections() {
  const [selectedCollection, setSelectedCollection] = useState<string | null>(null);
  const [selectedProperty, setSelectedProperty] = useState<string | null>(null);
  const { collections } = useCollections();

  if (collections.isLoading) {
    return <CollectionsSkeleton />;
  }

  if (collections.isError) {
    return <CollectionsError />;
  }

  const generalCollection = collections.data?.data.find((c) => c.name === "General");
  const otherCollections = collections.data?.data.filter((c) => c.name !== "General") || [];

  if (!generalCollection && otherCollections.length === 0) {
    return <EmptyCollections />;
  }

  return (
    <>
      {generalCollection && (
        <div className="mb-8">
          <div>
            <StyledCard
              className={`bg-[#010303]/70 border-gray-800 cursor-pointer transition-all duration-200 hover:bg-[#010303]/80 ${selectedCollection === generalCollection.id ? "ring-2 ring-blue-500/50" : ""
                }`}
              onClick={() =>
                setSelectedCollection(generalCollection.id === selectedCollection ? null : generalCollection.id)
              }
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-500/20 rounded-lg">
                    icon
                  </div>
                  <div>
                    <h3 className="font-medium text-white">{generalCollection.name}</h3>
                    <p className="text-sm text-gray-400">
                      {generalCollection.properties.length} property
                      {generalCollection.properties.length !== 1 ? "ies" : ""}
                    </p>
                  </div>
                </div>
                <button className="text-gray-400 hover:text-white">
                  icon
                </button>
              </div>

              {generalCollection.description && (
                <p className="text-sm text-gray-400 mb-4">{generalCollection.description}</p>
              )}

              {selectedCollection === generalCollection.id && (
                <div className="space-y-3 mt-4 pt-4 border-t border-gray-800">
                  {generalCollection.properties.length === 0 ? (
                    <p className="text-center text-gray-500 py-4">No properties yet</p>
                  ) : (
                    generalCollection.properties.map((property) => (
                      <div
                        key={property.id}
                        className="flex items-center justify-between p-3 rounded-lg bg-[#0a0e15] border border-gray-800/30"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-12 h-12 bg-gray-800/50 rounded-lg flex items-center justify-center">
                            icon
                          </div>
                          <div>
                            <h4 className="font-medium text-white">Property</h4>
                          </div>
                        </div>
                        <button
                          className="text-gray-400 hover:text-blue-400 hover:bg-blue-500/10"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedProperty(property.id);
                          }}
                        >
                          icon-property
                        </button>
                      </div>
                    ))
                  )}
                </div>
              )}
            </StyledCard>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
        {otherCollections.map((collection) => (
          <div key={collection.id}>
            <StyledCard
              className={`bg-[#010303]/50 border-gray-800 cursor-pointer transition-all duration-200 hover:bg-[#010303]/70 ${selectedCollection === collection.id ? "ring-2 ring-blue-500/50" : ""
                }`}
              onClick={() => setSelectedCollection(collection.id === selectedCollection ? null : collection.id)}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-500/10 rounded-lg">
                    icon
                  </div>
                  <div>
                    <h3 className="font-medium text-white">{collection.name}</h3>
                    <p className="text-sm text-gray-400">
                      {collection.properties.length} property{collection.properties.length !== 1 ? "ies" : ""}
                    </p>
                  </div>
                </div>
                <button className="text-gray-400 hover:text-white">
                  icon
                </button>
              </div>

              {collection.description && <p className="text-sm text-gray-400 mb-4">{collection.description}</p>}

              {selectedCollection === collection.id && (
                <div className="space-y-3 mt-4 pt-4 border-t border-gray-800">
                  {collection.properties.length === 0 ? (
                    <p className="text-center text-gray-500 py-4">No properties yet</p>
                  ) : (
                    collection.properties.map((property) => (
                      <div
                        key={property.id}
                        className="flex items-center justify-between p-3 rounded-lg bg-[#0a0e15] border border-gray-800/30"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-12 h-12 bg-gray-800/50 rounded-lg flex items-center justify-center">
                            icon
                          </div>
                          <div>
                            <h4 className="font-medium text-white">Property</h4>
                          </div>
                        </div>
                        <button
                          className="text-gray-400 hover:text-blue-400 hover:bg-blue-500/10"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedProperty(property.id);
                          }}
                        >
                          icon-info
                        </button>
                      </div>
                    ))
                  )}
                </div>
              )}
            </StyledCard>
          </div>
        ))}
      </div>

      <PropertyDetailsModal
        propertyId={selectedProperty}
        collectionId={selectedCollection}
        onClose={() => setSelectedProperty(null)}
      />
    </>
  );
}
