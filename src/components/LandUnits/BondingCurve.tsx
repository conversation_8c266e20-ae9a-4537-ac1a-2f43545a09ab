import BondingCurveViewer from "@/components/experimental/BondingCurveDisplay";
import React from "react";

export const LandUnitsBondingCurve = () => {
  return (
    <div className="w-full">
      <div className="mb-6">
        <h2 className="text-xl font-bold mb-2">Voronoi Bonding Curve Demo</h2>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <BondingCurveViewer testMode={true} voronoiId="A125478" />
        <BondingCurveViewer testMode={true} voronoiId="B982310" />
      </div>

      <div className="mt-8 p-4 bg-gray-800/30 rounded-lg border border-gray-700/50">
        <h3 className="font-semibold mb-2">How Voronoi Bonding Curves Work</h3>
        <p className="text-sm text-gray-400">
          Voronoi bonding curves create an automated market maker for each biome segment&apos;s tokens. The price
          increases as more tokens are purchased, and decreases as tokens are sold back to the contract. This creates a
          dynamic pricing mechanism that reflects the market demand for each voronoi cell, allowing participants to
          invest in specific geographical biomes.
        </p>
      </div>
    </div>
  );
};

export default LandUnitsBondingCurve;
