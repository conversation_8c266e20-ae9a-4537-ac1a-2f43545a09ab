// import MapMenu from "@/components/IbiUi/IbiMap/MapMenu";
import MapContainer from "@/components/MapContainer";
import { usePrimaryAccount } from "@/hooks/usePrimaryAccount";
import useWeather from "@/hooks/useWeather";
import { cn } from "@/lib/utils";
import { useMapSelector } from "@/stores/map.store";
import { usePrivy } from "@privy-io/react-auth";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

import GoogleSearchPlace from "../GoogleSearchInput";
import IbiIcon from "../IbiUi/IbiIcon";
import MapSideContent from "../IbiUi/IbiMap/MapSideContent";
import MobileNavigation from "../IbiUi/MobileNavigation";
import NavigationMenu from "../NavigationMenu";
import Notifications from "../Notifications";

const LegacyWrapper = ({ children }: { children: React.ReactNode }) => {
  const { logout } = usePrivy();
  const [sidebarState, setSidebarState] = useState({
    isContentOpen: true,
    isNavExpanded: false,
    isTransitioning: false,
  });
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const pathname = usePathname();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const currentMapType = useMemo<"default" | "biome" | "hydro">(() => {
    if (pathname === "/predict") {
      return "hydro";
    } else {
      return "default";
    }
  }, [pathname]);

  const setPadding = useMapSelector.use.setPadding();

  useEffect(() => {
    if (sidebarState.isContentOpen) {
      setPadding({
        right: 426,
        top: 56,
        bottom: 0,
        left: 0,
      });
    }
  }, [setPadding, sidebarState.isContentOpen]);

  const handleNavInteraction = useCallback((isExpanding: boolean) => {
    setSidebarState((prev) => ({
      ...prev,
      isTransitioning: true,
      isNavExpanded: isExpanding,
    }));

    setTimeout(() => {
      setSidebarState((prev) => ({
        ...prev,
        isTransitioning: false,
      }));
    }, 300);
  }, []);

  const {
    loadingWeather: loading,
    failedToFetchWeather,
    temperature,
    weatherIcon,
    currentAddress: address,
  } = useWeather();

  const { avatarLetter } = usePrimaryAccount();

  const toggleDropdown = () => {
    setDropdownOpen((prev) => !prev);
  };

  const renderWorldView = () => (
    <div className="relative h-full overflow-hidden">
      <div className="absolute inset-0">
        <MapContainer currentMapType={currentMapType} />
      </div>

      <MobileNavigation />

      <div
        className={cn(
          "fixed left-0 top-0 h-screen flex-col transition-all duration-300 ease-in-out bg-black/85 backdrop-blur-xl shadow-lg z-20 border-r-[1px] border-r-[#ffffff2a] hidden min-[750px]:flex",
          {
            "w-[200px]": sidebarState.isNavExpanded,
            "w-[55px]": !sidebarState.isNavExpanded,
          },
        )}
        onMouseEnter={() => handleNavInteraction(true)}
        onMouseLeave={() => handleNavInteraction(false)}
      >
        <div
          className={cn("h-full flex flex-col", {
            "items-start": sidebarState.isNavExpanded,
            "items-center": !sidebarState.isNavExpanded,
          })}
        >
          <NavigationMenu open={sidebarState.isNavExpanded} />
        </div>
      </div>

      <header className="fixed top-0 bg-transparent left-0 min-[750px]:left-[55px] right-0 h-14 px-6 gap-3 hidden min-[750px]:flex  z-10">
        <div className="flex flex-col h-full justify-center">
          <div className="flex items-center gap-10">
            <div className="flex items-center gap-6 h-full">
              {/* <div className="flex items-center gap-3">
                <MapBreadcrumb />
              </div> */}
              {/* <hr className="h-[30%] border-[#ffffff5e] border-l-[0.1rem]" /> */}
              <GoogleSearchPlace />
            </div>
          </div>
          {/* <MapMenu currentMapType={currentMapType} setCurrentMapType={setCurrentMapType} /> */}
        </div>
        <div className="flex gap-6 h-full flex-1 items-center justify-end">
          <div className="flex items-center gap-6">
            {address.data && (
              <div className="flex items-center gap-1.5 font-[700] [&>p]:text-[12px]">
                <p>
                  {address.data[0]?.locality} {address.data[0]?.region_code},{" "}
                </p>
                <p>{address.data[0]?.country}</p>
              </div>
            )}
            {!failedToFetchWeather && (
              <>
                {loading || !temperature() ? (
                  <IbiIcon icon="eos-icons:three-dots-loading" />
                ) : (
                  <div className="flex items-center gap-1.5 text-[14px] font-[700]">
                    <IbiIcon icon={weatherIcon()} className="text-lg" />
                    <p>{temperature()}</p>
                  </div>
                )}
              </>
            )}
          </div>
          <div className="flex items-center gap-1.5">
            <button className="text-white/90 text-sm p-2 rounded-md hover:text-white hover:bg-black/80 flex items-center gap-1.5 transition-all duration-200 ease-in">
              <div className="flex items-center gap-1.5">
                <IbiIcon icon="mdi:ethereum" className="w-4 h-4" />
                <span>0.003 ETH</span>
                <hr className="h-[10px] border-[#ffffff5e] border-l-[0.1rem] mx-1" />
                <span>0.00 WETH</span>
              </div>
            </button>
          </div>
          <hr className="h-[30%] border-[#ffffff5e] border-l-[0.1rem]" />
          <Notifications />
          <hr className="h-[30%] border-[#ffffff5e] border-l-[0.1rem]" />
          <div className="relative" ref={dropdownRef}>
            <button onClick={toggleDropdown} className="flex items-center gap-4">
              <div className="h-10 w-10 rounded-full bg-blue-950 flex items-center justify-center text-white">
                {avatarLetter}
              </div>
            </button>
            {dropdownOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-black/85 backdrop-blur-xl rounded-md shadow-lg z-50 border border-[#ffffff2a]">
                <div className="py-1">
                  <button className="w-full text-left block px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]">
                    Add Wallet
                  </button>
                  <Link href="/overview" className="block px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]">
                    Dashboard
                  </Link>
                  <Link href="/rewards" className="block px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]">
                    Rewards
                  </Link>
                  <Link href="/invite-codes" className="block px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]">
                    Referral
                  </Link>
                  <Link href="/landunits" className="block px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]">
                    Backpack
                  </Link>
                  <Link href="/manage-accounts" className="block px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]">
                    Account
                  </Link>
                  <span
                    onClick={logout}
                    className="flex items-center cursor-pointer px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]"
                  >
                    <IbiIcon icon="mdi:logout" className="mr-2 w-4 h-4 -mt-0.5 " />
                    Logout
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </header>

      <div className="fixed right-0 top-0 min-[750px]:top-14 bottom-0 overflow-visible">
        <aside
          className={cn(
            "w-[280px] min-[440px]:w-[426px] bg-[#01050dD9] backdrop-blur-md  transition-all duration-300 ease-in-out",
          )}
        >
          <div className="h-full overflow-auto">
            <MapSideContent currentMapType={currentMapType} />
          </div>
        </aside>
      </div>
    </div>
  );

  const renderPredictView = () => (
    <div className="relative h-full overflow-hidden">
      <div className="absolute inset-0">{children}</div>

      <MobileNavigation />

      <div
        className={cn(
          "fixed left-0 top-0 h-screen flex-col transition-all duration-300 ease-in-out bg-black/85 backdrop-blur-xl shadow-lg z-20 border-r-[1px] border-r-[#ffffff2a] hidden min-[750px]:flex",
          {
            "w-[200px]": sidebarState.isNavExpanded,
            "w-[55px]": !sidebarState.isNavExpanded,
          },
        )}
        onMouseEnter={() => handleNavInteraction(true)}
        onMouseLeave={() => handleNavInteraction(false)}
      >
        <div
          className={cn("h-full flex flex-col", {
            "items-start": sidebarState.isNavExpanded,
            "items-center": !sidebarState.isNavExpanded,
          })}
        >
          <NavigationMenu open={sidebarState.isNavExpanded} />
        </div>
      </div>

      <header className="fixed top-0 bg-transparent left-0 min-[750px]:left-[55px] right-0 h-14 px-6 gap-3 hidden min-[750px]:flex  z-10">
        <div className="flex flex-col h-full justify-center">
          <div className="flex items-center gap-10">
            <div className="flex items-center gap-6 h-full">
              {/* <div className="flex items-center gap-3">
                <MapBreadcrumb />
              </div> */}
              {/* <hr className="h-[30%] border-[#ffffff5e] border-l-[0.1rem]" /> */}
              <GoogleSearchPlace />
            </div>
          </div>
          {/* <MapMenu currentMapType={currentMapType} setCurrentMapType={setCurrentMapType} /> */}
        </div>
        <div className="flex gap-6 h-full flex-1 items-center justify-end">
          <div className="flex items-center gap-6">
            {address.data && (
              <div className="flex items-center gap-1.5 font-[700] [&>p]:text-[12px]">
                <p>
                  {address.data[0]?.locality} {address.data[0]?.region_code},{" "}
                </p>
                <p>{address.data[0]?.country}</p>
              </div>
            )}
            {!failedToFetchWeather && (
              <>
                {loading || !temperature() ? (
                  <IbiIcon icon="eos-icons:three-dots-loading" />
                ) : (
                  <div className="flex items-center gap-1.5 text-[14px] font-[700]">
                    <IbiIcon icon={weatherIcon()} className="text-lg" />
                    <p>{temperature()}</p>
                  </div>
                )}
              </>
            )}
          </div>
          <div className="flex items-center gap-1.5">
            <button className="text-white/90 text-sm p-2 rounded-md hover:text-white hover:bg-black/80 flex items-center gap-1.5 transition-all duration-200 ease-in">
              <div className="flex items-center gap-1.5">
                <IbiIcon icon="mdi:ethereum" className="w-4 h-4" />
                <span>0.003 ETH</span>
                <hr className="h-[10px] border-[#ffffff5e] border-l-[0.1rem] mx-1" />
                <span>0.00 WETH</span>
              </div>
            </button>
          </div>
          <hr className="h-[30%] border-[#ffffff5e] border-l-[0.1rem]" />
          <Notifications />
          <hr className="h-[30%] border-[#ffffff5e] border-l-[0.1rem]" />
          <div className="relative" ref={dropdownRef}>
            <button onClick={toggleDropdown} className="flex items-center gap-4">
              <div className="h-10 w-10 rounded-full bg-blue-950 flex items-center justify-center text-white">
                {avatarLetter}
              </div>
            </button>
            {dropdownOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-black/85 backdrop-blur-xl rounded-md shadow-lg z-50 border border-[#ffffff2a]">
                <div className="py-1">
                  <button className="w-full text-left block px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]">
                    Add Wallet
                  </button>
                  <Link href="/overview" className="block px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]">
                    Dashboard
                  </Link>
                  <Link href="/rewards" className="block px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]">
                    Rewards
                  </Link>
                  <Link href="/invite-codes" className="block px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]">
                    Referral
                  </Link>
                  <Link href="/landunits" className="block px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]">
                    Backpack
                  </Link>
                  <Link href="/manage-accounts" className="block px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]">
                    Account
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      </header>

      <div className="fixed right-0 top-0 min-[750px]:top-14 bottom-0 overflow-visible">
        <aside
          className={cn(
            "w-[280px] min-[440px]:w-[426px] bg-[#01050dD9] backdrop-blur-md border-r border-r-[#CFD4F733] transition-all duration-300 ease-in-out",
          )}
        >
          <div className="h-full overflow-auto">
            <MapSideContent currentMapType={currentMapType} />
          </div>
        </aside>
      </div>
    </div>
  );

  const renderDefaultView = () => (
    <div className="h-full flex overflow-hidden">
      <div
        className={cn(
          "flex-shrink-0 transition-all duration-300 ease-in-out bg-[#010303] hidden min-[750px]:block z-10 border-r border-r-[#242424]",
          {
            "w-[200px]": sidebarState.isNavExpanded,
            "w-[55px]": !sidebarState.isNavExpanded,
          },
        )}
        onMouseEnter={() => handleNavInteraction(true)}
        onMouseLeave={() => handleNavInteraction(false)}
      >
        <div
          className={cn("h-full flex-col flex", {
            "items-start": sidebarState.isNavExpanded,
            "items-center": !sidebarState.isNavExpanded,
          })}
        >
          <NavigationMenu open={sidebarState.isNavExpanded} />
        </div>
      </div>

      <MobileNavigation />

      <main className="flex-1 h-full overflow-auto">{children}</main>
    </div>
  );

  const renderBondView = () => (
    <div className="relative h-full overflow-hidden">
      <div className="absolute inset-0">{children}</div>

      <MobileNavigation />

      <div
        className={cn(
          "fixed left-0 top-0 h-screen flex-col transition-all duration-300 ease-in-out bg-black/85 backdrop-blur-xl shadow-lg z-20 border-r-[1px] border-r-[#ffffff2a] hidden min-[750px]:flex",
          {
            "w-[200px]": sidebarState.isNavExpanded,
            "w-[55px]": !sidebarState.isNavExpanded,
          },
        )}
        onMouseEnter={() => handleNavInteraction(true)}
        onMouseLeave={() => handleNavInteraction(false)}
      >
        <div
          className={cn("h-full flex flex-col", {
            "items-start": sidebarState.isNavExpanded,
            "items-center": !sidebarState.isNavExpanded,
          })}
        >
          <NavigationMenu open={sidebarState.isNavExpanded} />
        </div>
      </div>

      <header className="fixed top-0 bg-transparent left-0 min-[750px]:left-[55px] right-0 h-14 px-6 gap-3 hidden min-[750px]:flex  z-10">
        <div className="flex flex-col h-full justify-center">
          <div className="flex items-center gap-10">
            <div className="flex items-center gap-6 h-full">
              <GoogleSearchPlace />
            </div>
          </div>
        </div>
        <div className="flex gap-6 h-full flex-1 items-center justify-end">
          <div className="flex items-center gap-6">
            {address.data && (
              <div className="flex items-center gap-1.5 font-[700] [&>p]:text-[12px]">
                <p>
                  {address.data[0]?.locality} {address.data[0]?.region_code},{" "}
                </p>
                <p>{address.data[0]?.country}</p>
              </div>
            )}
            {!failedToFetchWeather && (
              <>
                {loading || !temperature() ? (
                  <IbiIcon icon="eos-icons:three-dots-loading" />
                ) : (
                  <div className="flex items-center gap-1.5 text-[14px] font-[700]">
                    <IbiIcon icon={weatherIcon()} className="text-lg" />
                    <p>{temperature()}</p>
                  </div>
                )}
              </>
            )}
          </div>
          <div className="flex items-center gap-1.5">
            <button className="text-white/90 text-sm p-2 rounded-md hover:text-white hover:bg-black/80 flex items-center gap-1.5 transition-all duration-200 ease-in">
              <div className="flex items-center gap-1.5">
                <IbiIcon icon="mdi:ethereum" className="w-4 h-4" />
                <span>0.003 ETH</span>
                <hr className="h-[10px] border-[#ffffff5e] border-l-[0.1rem] mx-1" />
                <span>0.00 WETH</span>
              </div>
            </button>
          </div>
          <hr className="h-[30%] border-[#ffffff5e] border-l-[0.1rem]" />
          <Notifications />
          <hr className="h-[30%] border-[#ffffff5e] border-l-[0.1rem]" />
          <div className="relative" ref={dropdownRef}>
            <button onClick={toggleDropdown} className="flex items-center gap-4">
              <div className="h-10 w-10 rounded-full bg-blue-950 flex items-center justify-center text-white">
                {avatarLetter}
              </div>
            </button>
            {dropdownOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-black/85 backdrop-blur-xl rounded-md shadow-lg z-50 border border-[#ffffff2a]">
                <div className="py-1">
                  <button className="w-full text-left block px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]">
                    Add Wallet
                  </button>
                  <Link href="/overview" className="block px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]">
                    Dashboard
                  </Link>
                  <Link href="/rewards" className="block px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]">
                    Rewards
                  </Link>
                  <Link href="/invite-codes" className="block px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]">
                    Referral
                  </Link>
                  <Link href="/landunits" className="block px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]">
                    Backpack
                  </Link>
                  <Link href="/manage-accounts" className="block px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]">
                    Account
                  </Link>
                  <span
                    onClick={logout}
                    className="flex items-center cursor-pointer px-4 py-2 text-sm text-white hover:bg-[#ffffff1a]"
                  >
                    <IbiIcon icon="mdi:logout" className="mr-2 w-4 h-4 -mt-0.5 " />
                    Logout
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </header>
    </div>
  );

  return (
    <div className="h-full relative">
      {pathname === "/claim"
        ? renderWorldView()
        : pathname === "/predict"
          ? renderPredictView()
          : pathname === "/bond"
            ? renderBondView()
            : renderDefaultView()}
    </div>
  );
};

export default LegacyWrapper;
