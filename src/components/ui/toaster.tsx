"use client";

import { Toast, ToastClose, ToastDescription, ToastProvider, ToastTitle, ToastViewport } from "@/components/ui/toast";
import { useToast } from "@/hooks/useToast";

export function Toaster() {
  const { toasts } = useToast();

  // Pegamos o primeiro toast para definir a posição e margem do viewport
  const activeToast = toasts[0];

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, exitDirection, backgroundColor, ...props }) {
        return (
          <Toast
            key={id}
            {...props}
            exitDirection={exitDirection}
            style={{ backgroundColor: backgroundColor ?? "#01050dD9" }}
            className="text-white"
          >
            <div className="grid gap-1">
              {title && <ToastTitle>{title}</ToastTitle>}
              {description && <ToastDescription>{description}</ToastDescription>}
            </div>
            {action}
            <ToastClose />
          </Toast>
        );
      })}
      <ToastViewport position={activeToast?.position} margin={activeToast?.margin} />
    </ToastProvider>
  );
}
