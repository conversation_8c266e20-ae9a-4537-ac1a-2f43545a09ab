export const StyledCard = ({
    className = "",
    children,
    onClick = () => { },
    ...props
}) => {
    const baseStyle = {
        borderRadius: "0.75rem",
        border: "1px solid",
        borderColor: "#262626",
        backgroundColor: "#000000",
        color: "#fafafa",
        boxShadow: "0 1px 3px rgba(0,0,0,0.12)",
        padding: "1.5rem"
    };

    return (
        <div
            style={baseStyle}
            className={className}
            onClick={onClick}
            {...props}
        >
            {children}
        </div>
    );
};