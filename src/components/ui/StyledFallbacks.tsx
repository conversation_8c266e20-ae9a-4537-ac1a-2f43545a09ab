// StyledFallbacks.tsx - Fallback components with inline styles to replace shadcn/ui components
import React from "react";

export const StyledCard = ({
    className = "",
    children,
    onClick = () => { },
    ...props
}) => {
    const baseStyle = {
        borderRadius: "0.75rem",
        border: "1px solid",
        borderColor: "#262626",
        backgroundColor: "#000000",
        color: "#fafafa",
        boxShadow: "0 1px 3px rgba(0,0,0,0.12)",
        padding: "1.5rem"
    };

    return (
        <div
            style={baseStyle}
            className={className}
            onClick={onClick}
            {...props}
        >
            {children}
        </div>
    );
};

export const StyledButton = ({
    className = "",
    children,
    onClick = (e) => { },
    disabled = false,
    variant = "default",
    size = "default",
    ...props
}) => {
    const getVariantStyles = () => {
        switch (variant) {
            case "outline":
                return {
                    backgroundColor: "transparent",
                    border: "1px solid #374151",
                    color: "#d1d5db"
                };
            case "ghost":
                return {
                    backgroundColor: "transparent",
                    border: "none",
                    color: "#9ca3af"
                };
            default:
                return {
                    backgroundColor: "#1f2937",
                    border: "1px solid #374151",
                    color: "#ffffff"
                };
        }
    };

    const getSizeStyles = () => {
        switch (size) {
            case "sm":
                return {
                    padding: "0.25rem 0.75rem",
                    fontSize: "0.875rem"
                };
            default:
                return {
                    padding: "0.5rem 1rem",
                    fontSize: "0.875rem"
                };
        }
    };

    const baseStyle = {
        borderRadius: "0.375rem",
        fontWeight: "500",
        cursor: disabled ? "not-allowed" : "pointer",
        opacity: disabled ? 0.5 : 1,
        transition: "all 0.2s",
        display: "inline-flex",
        alignItems: "center",
        justifyContent: "center",
        ...getVariantStyles(),
        ...getSizeStyles()
    };

    return (
        <button
            style={baseStyle}
            className={className}
            onClick={disabled ? undefined : onClick}
            disabled={disabled}
            {...props}
        >
            {children}
        </button>
    );
};

export const StyledTable = ({ className = "", children, ...props }) => {
    const baseStyle = {
        width: "100%",
        borderCollapse: "collapse" as const
    };

    return (
        <table
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </table>
    );
};

export const StyledTableHeader = ({ className = "", children, ...props }) => {
    return (
        <thead className={className} {...props}>
            {children}
        </thead>
    );
};

export const StyledTableBody = ({ className = "", children, ...props }) => {
    return (
        <tbody className={className} {...props}>
            {children}
        </tbody>
    );
};

export const StyledTableRow = ({ className = "", children, onClick, ...props }: {
    className?: string;
    children: any;
    onClick?: () => void;
    [key: string]: any;
}) => {
    const baseStyle = {
        borderBottom: "1px solid #374151",
        transition: "background-color 0.2s",
        cursor: onClick ? "pointer" : "default"
    };

    return (
        <tr
            style={baseStyle}
            className={className}
            onClick={onClick}
            {...props}
        >
            {children}
        </tr>
    );
};

export const StyledTableHead = ({ className = "", children, ...props }) => {
    const baseStyle = {
        padding: "0.75rem",
        textAlign: "left" as const,
        fontWeight: "500",
        fontSize: "0.875rem"
    };

    return (
        <th
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </th>
    );
};

export const StyledTableCell = ({ className = "", children, ...props }) => {
    const baseStyle = {
        padding: "0.75rem",
        fontSize: "0.875rem"
    };

    return (
        <td
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </td>
    );
};

// Avatar Components
export const StyledAvatar = ({ className = "", children, ...props }) => {
    const baseStyle = {
        position: "relative" as const,
        display: "flex",
        height: "2.5rem",
        width: "2.5rem",
        flexShrink: 0,
        overflow: "hidden",
        borderRadius: "50%"
    };

    return (
        <div
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </div>
    );
};

export const StyledAvatarImage = ({ className = "", src, alt, ...props }) => {
    const baseStyle = {
        aspectRatio: "1",
        height: "100%",
        width: "100%",
        objectFit: "cover" as const
    };

    return (
        <img
            style={baseStyle}
            className={className}
            src={src}
            alt={alt}
            {...props}
        />
    );
};

export const StyledAvatarFallback = ({ className = "", children, ...props }) => {
    const baseStyle = {
        display: "flex",
        height: "100%",
        width: "100%",
        alignItems: "center",
        justifyContent: "center",
        borderRadius: "50%",
        backgroundColor: "#1f2937",
        color: "#ffffff",
        fontSize: "0.875rem",
        fontWeight: "500"
    };

    return (
        <div
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </div>
    );
};

// Badge Component
export const StyledBadge = ({ className = "", children, variant = "default", ...props }) => {
    const getVariantStyles = () => {
        switch (variant) {
            case "secondary":
                return {
                    backgroundColor: "#374151",
                    color: "#d1d5db"
                };
            case "destructive":
                return {
                    backgroundColor: "#dc2626",
                    color: "#ffffff"
                };
            default:
                return {
                    backgroundColor: "#1f2937",
                    color: "#ffffff"
                };
        }
    };

    const baseStyle = {
        display: "inline-flex",
        alignItems: "center",
        borderRadius: "0.375rem",
        border: "1px solid #374151",
        padding: "0.125rem 0.625rem",
        fontSize: "0.75rem",
        fontWeight: "600",
        transition: "colors",
        ...getVariantStyles()
    };

    return (
        <div
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </div>
    );
};

// Dialog Components
export const StyledDialog = ({ open, onOpenChange, children, ...props }) => {
    if (!open) return null;

    return (
        <div
            style={{
                position: "fixed",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: "rgba(0, 0, 0, 0.8)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                zIndex: 50
            }}
            onClick={() => onOpenChange?.(false)}
            {...props}
        >
            {children}
        </div>
    );
};

export const StyledDialogContent = ({ className = "", children, hideClose = false, ...props }) => {
    const baseStyle = {
        position: "relative" as const,
        width: "100%",
        maxWidth: "32rem",
        backgroundColor: "#111827",
        border: "1px solid #374151",
        borderRadius: "0.5rem",
        padding: "1.5rem",
        boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
        color: "#ffffff"
    };

    return (
        <div
            style={baseStyle}
            className={className}
            onClick={(e) => e.stopPropagation()}
            {...props}
        >
            {!hideClose && (
                <button
                    style={{
                        position: "absolute",
                        top: "1rem",
                        right: "1rem",
                        background: "none",
                        border: "none",
                        cursor: "pointer",
                        color: "#9ca3af",
                        fontSize: "1.5rem"
                    }}
                    onClick={() => props.onClose?.()}
                >
                    ×
                </button>
            )}
            {children}
        </div>
    );
};

export const StyledDialogHeader = ({ className = "", children, ...props }) => {
    const baseStyle = {
        display: "flex",
        flexDirection: "column" as const,
        gap: "0.375rem",
        marginBottom: "1rem"
    };

    return (
        <div
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </div>
    );
};

export const StyledDialogTitle = ({ className = "", children, ...props }) => {
    const baseStyle = {
        fontSize: "1.125rem",
        fontWeight: "600",
        color: "#ffffff"
    };

    return (
        <h2
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </h2>
    );
};

export const StyledDialogDescription = ({ className = "", children, ...props }) => {
    const baseStyle = {
        fontSize: "0.875rem",
        color: "#9ca3af"
    };

    return (
        <p
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </p>
    );
};

export const StyledDialogFooter = ({ className = "", children, ...props }) => {
    const baseStyle = {
        display: "flex",
        gap: "0.5rem",
        justifyContent: "flex-end",
        marginTop: "1rem"
    };

    return (
        <div
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </div>
    );
};

// Tabs Components (Simplified - manage state externally)
export const StyledTabs = ({ className = "", children, ...props }) => {
    return (
        <div className={className} {...props}>
            {children}
        </div>
    );
};

export const StyledTabsList = ({ className = "", children, ...props }) => {
    const baseStyle = {
        display: "inline-flex",
        height: "2.25rem",
        alignItems: "center",
        justifyContent: "center",
        borderRadius: "0.5rem",
        backgroundColor: "#1f2937",
        padding: "0.25rem",
        color: "#9ca3af"
    };

    return (
        <div
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </div>
    );
};

export const StyledTabsTrigger = ({ value, className = "", children, onClick, active = false, ...props }) => {
    const baseStyle = {
        display: "inline-flex",
        alignItems: "center",
        justifyContent: "center",
        whiteSpace: "nowrap" as const,
        borderRadius: "0.375rem",
        padding: "0.375rem 0.75rem",
        fontSize: "0.875rem",
        fontWeight: "500",
        cursor: "pointer",
        transition: "all 0.2s",
        backgroundColor: active ? "#111827" : "transparent",
        color: active ? "#ffffff" : "#9ca3af",
        boxShadow: active ? "0 1px 3px rgba(0, 0, 0, 0.1)" : "none",
        border: "none"
    };

    return (
        <button
            style={baseStyle}
            className={className}
            onClick={onClick}
            {...props}
        >
            {children}
        </button>
    );
};

export const StyledTabsContent = ({ className = "", children, ...props }) => {
    const baseStyle = {
        marginTop: "0.5rem"
    };

    return (
        <div
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </div>
    );
};

// Skeleton Component
export const StyledSkeleton = ({ className = "", ...props }) => {
    const baseStyle = {
        animation: "pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        borderRadius: "0.375rem",
        backgroundColor: "rgba(55, 65, 81, 0.3)"
    };

    return (
        <div
            style={baseStyle}
            className={className}
            {...props}
        />
    );
};

// Input Components
export const StyledInput = ({ className = "", type = "text", ...props }) => {
    const baseStyle = {
        display: "flex",
        height: "2.25rem",
        width: "100%",
        borderRadius: "0.375rem",
        border: "1px solid #374151",
        backgroundColor: "transparent",
        padding: "0.5rem 0.75rem",
        fontSize: "0.875rem",
        color: "#ffffff",
        outline: "none",
        transition: "border-color 0.2s"
    };

    return (
        <input
            type={type}
            style={baseStyle}
            className={className}
            {...props}
        />
    );
};

export const StyledLabel = ({ className = "", children, ...props }) => {
    const baseStyle = {
        fontSize: "0.875rem",
        fontWeight: "500",
        color: "#ffffff",
        marginBottom: "0.5rem",
        display: "block"
    };

    return (
        <label
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </label>
    );
};

export const StyledTextarea = ({ className = "", ...props }) => {
    const baseStyle = {
        display: "flex",
        minHeight: "5rem",
        width: "100%",
        borderRadius: "0.375rem",
        border: "1px solid #374151",
        backgroundColor: "transparent",
        padding: "0.5rem 0.75rem",
        fontSize: "0.875rem",
        color: "#ffffff",
        outline: "none",
        resize: "vertical" as const,
        transition: "border-color 0.2s"
    };

    return (
        <textarea
            style={baseStyle}
            className={className}
            {...props}
        />
    );
};

// Progress Component
export const StyledProgress = ({ value = 0, className = "", ...props }) => {
    const baseStyle = {
        position: "relative" as const,
        height: "0.5rem",
        width: "100%",
        overflow: "hidden",
        borderRadius: "9999px",
        backgroundColor: "#374151"
    };

    const fillStyle = {
        height: "100%",
        width: `${Math.min(100, Math.max(0, value))}%`,
        backgroundColor: "#3b82f6",
        transition: "width 0.3s ease-in-out"
    };

    return (
        <div
            style={baseStyle}
            className={className}
            {...props}
        >
            <div style={fillStyle} />
        </div>
    );
};

// DropdownMenu Components (Simplified - manage state externally)
export const StyledDropdownMenu = ({ children, className = "", ...props }) => {
    return (
        <div
            style={{ position: "relative", display: "inline-block" }}
            className={className}
            {...props}
        >
            {children}
        </div>
    );
};

export const StyledDropdownMenuTrigger = ({ children, onClick, className = "", ...props }) => {
    return (
        <div
            onClick={onClick}
            className={className}
            {...props}
        >
            {children}
        </div>
    );
};

export const StyledDropdownMenuContent = ({ children, align = "end", show = false, className = "", ...props }) => {
    if (!show) return null;

    const alignmentStyles = {
        start: { left: 0 },
        end: { right: 0 },
        center: { left: "50%", transform: "translateX(-50%)" }
    };

    const baseStyle = {
        position: "absolute" as const,
        top: "100%",
        marginTop: "0.25rem",
        minWidth: "8rem",
        backgroundColor: "#1f2937",
        border: "1px solid #374151",
        borderRadius: "0.375rem",
        padding: "0.25rem",
        boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
        zIndex: 50,
        ...alignmentStyles[align]
    };

    return (
        <div
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </div>
    );
};

export const StyledDropdownMenuItem = ({ children, onClick, className = "", ...props }) => {
    const baseStyle = {
        display: "flex",
        alignItems: "center",
        padding: "0.375rem 0.5rem",
        fontSize: "0.875rem",
        cursor: "pointer",
        borderRadius: "0.25rem",
        color: "#d1d5db",
        transition: "background-color 0.2s, color 0.2s"
    };

    return (
        <div
            style={baseStyle}
            className={`${className} hover:bg-slate-700 hover:text-white`}
            onClick={onClick}
            {...props}
        >
            {children}
        </div>
    );
};

// Card sub-components
export const StyledCardHeader = ({ className = "", children, ...props }) => {
    const baseStyle = {
        display: "flex",
        flexDirection: "column" as const,
        gap: "0.375rem",
        padding: "1.5rem",
        paddingBottom: "0"
    };

    return (
        <div
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </div>
    );
};

export const StyledCardTitle = ({ className = "", children, ...props }) => {
    const baseStyle = {
        fontSize: "1.125rem",
        fontWeight: "600",
        lineHeight: "1.25",
        color: "#ffffff"
    };

    return (
        <h3
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </h3>
    );
};

export const StyledCardContent = ({ className = "", children, ...props }) => {
    const baseStyle = {
        padding: "1.5rem",
        paddingTop: "0"
    };

    return (
        <div
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </div>
    );
};