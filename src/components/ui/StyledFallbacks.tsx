export const StyledCard = ({
    className = "",
    children,
    onClick = () => { },
    ...props
}) => {
    const baseStyle = {
        borderRadius: "0.75rem",
        border: "1px solid",
        borderColor: "#262626",
        backgroundColor: "#000000",
        color: "#fafafa",
        boxShadow: "0 1px 3px rgba(0,0,0,0.12)",
        padding: "1.5rem"
    };

    return (
        <div
            style={baseStyle}
            className={className}
            onClick={onClick}
            {...props}
        >
            {children}
        </div>
    );
};

export const StyledButton = ({
    className = "",
    children,
    onClick = () => { },
    disabled = false,
    variant = "default",
    size = "default",
    ...props
}) => {
    const getVariantStyles = () => {
        switch (variant) {
            case "outline":
                return {
                    backgroundColor: "transparent",
                    border: "1px solid #374151",
                    color: "#d1d5db"
                };
            case "ghost":
                return {
                    backgroundColor: "transparent",
                    border: "none",
                    color: "#9ca3af"
                };
            default:
                return {
                    backgroundColor: "#1f2937",
                    border: "1px solid #374151",
                    color: "#ffffff"
                };
        }
    };

    const getSizeStyles = () => {
        switch (size) {
            case "sm":
                return {
                    padding: "0.25rem 0.75rem",
                    fontSize: "0.875rem"
                };
            default:
                return {
                    padding: "0.5rem 1rem",
                    fontSize: "0.875rem"
                };
        }
    };

    const baseStyle = {
        borderRadius: "0.375rem",
        fontWeight: "500",
        cursor: disabled ? "not-allowed" : "pointer",
        opacity: disabled ? 0.5 : 1,
        transition: "all 0.2s",
        display: "inline-flex",
        alignItems: "center",
        justifyContent: "center",
        ...getVariantStyles(),
        ...getSizeStyles()
    };

    return (
        <button
            style={baseStyle}
            className={className}
            onClick={disabled ? undefined : onClick}
            disabled={disabled}
            {...props}
        >
            {children}
        </button>
    );
};

export const StyledTable = ({ className = "", children, ...props }) => {
    const baseStyle = {
        width: "100%",
        borderCollapse: "collapse" as const
    };

    return (
        <table
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </table>
    );
};

export const StyledTableHeader = ({ className = "", children, ...props }) => {
    return (
        <thead className={className} {...props}>
            {children}
        </thead>
    );
};

export const StyledTableBody = ({ className = "", children, ...props }) => {
    return (
        <tbody className={className} {...props}>
            {children}
        </tbody>
    );
};

export const StyledTableRow = ({ className = "", children, onClick, ...props }: {
    className?: string;
    children: any;
    onClick?: () => void;
    [key: string]: any;
}) => {
    const baseStyle = {
        borderBottom: "1px solid #374151",
        transition: "background-color 0.2s",
        cursor: onClick ? "pointer" : "default"
    };

    return (
        <tr
            style={baseStyle}
            className={className}
            onClick={onClick}
            {...props}
        >
            {children}
        </tr>
    );
};

export const StyledTableHead = ({ className = "", children, ...props }) => {
    const baseStyle = {
        padding: "0.75rem",
        textAlign: "left" as const,
        fontWeight: "500",
        fontSize: "0.875rem"
    };

    return (
        <th
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </th>
    );
};

export const StyledTableCell = ({ className = "", children, ...props }) => {
    const baseStyle = {
        padding: "0.75rem",
        fontSize: "0.875rem"
    };

    return (
        <td
            style={baseStyle}
            className={className}
            {...props}
        >
            {children}
        </td>
    );
};