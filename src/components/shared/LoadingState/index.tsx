"use client";

import IbiIcon from "@/components/IbiUi/IbiIcon";
import { cn } from "@/lib/utils";

interface LoadingStateProps {
  text?: string;
  className?: string;
  variant?: "fullscreen" | "inline";
}

export default function LoadingState({
  text = "Forest Loading...",
  className,
  variant = "fullscreen",
}: LoadingStateProps) {
  if (variant === "inline") {
    return (
      <div className={cn("flex items-center gap-3 bg-black/50 p-3 rounded-lg", className)}>
        <IbiIcon icon="svg-spinners:pulse-rings-3" className="text-2xl text-orange-1" />
        <p className="text-sm text-neutral-7">{text}</p>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "absolute z-[100] w-full h-dvh bg-black flex justify-center items-center flex-col gap-4",
        className,
      )}
    >
      <h1 className="font-extrabold text-5xl">YBYCASH</h1>
      <IbiIcon icon="svg-spinners:pulse-rings-3" className="text-4xl text-orange-1" />
      <p className="text-lg text-neutral-7">{text}</p>
    </div>
  );
}
