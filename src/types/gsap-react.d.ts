declare module "@gsap/react" {
  import { MutableRefObject } from "react";

  export interface UseGSAPConfig {
    scope?: MutableRefObject<any>;
    dependencies?: any[];
    revertOnUpdate?: boolean;
  }

  export type ContextSafeFunction = <T extends (...args: any[]) => any>(fn: T) => T;

  export interface GSAPContext {
    add(animation: any): void;
    revert(): void;
  }

  export interface UseGSAPReturn {
    context: GSAPContext;
    contextSafe: ContextSafeFunction;
  }

  export type UseGSAPHook = {
    (callback: (context: GSAPContext, contextSafe: ContextSafeFunction) => void): void;
    (callback: (context: GSAPContext, contextSafe: ContextSafeFunction) => void, config: UseGSAPConfig): void;
    (callback: () => void, dependencies: any[]): void;
    (config: UseGSAPConfig): UseGSAPReturn;
  };

  export const useGSAP: UseGSAPHook;
}
