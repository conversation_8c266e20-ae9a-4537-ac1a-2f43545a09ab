declare module "gsap" {
  export interface GSAPAnimation {
    progress: number;
    duration: number;
    repeat: number;
    delay: number;
    yoyo: boolean;
    paused: boolean;
    reversed: boolean;
    timeScale: number;
    play(): void;
    pause(): void;
    resume(): void;
    reverse(): void;
    restart(): void;
    progress(value: number): void;
    totalProgress(value: number): void;
    kill(): void;
    time(value: number): void;
    totalTime(value: number): void;
    time(): number;
    totalTime(): number;
    duration(value: number): void;
    totalDuration(value: number): void;
    isActive(): boolean;
  }

  export interface GSAPContext {
    add(animation: any): void;
    revert(): void;
  }

  export interface GSAPTween extends GSAPAnimation {
    vars: Record<string, any>;
    target: any;
    targets(): any[];
  }

  export interface GSAPTimeline extends GSAPAnimation {
    add(animation: any, position?: any): void;
    to(target: any, vars: any, position?: any): GSAPTimeline;
    from(target: any, vars: any, position?: any): GSAPTimeline;
    fromTo(target: any, fromVars: any, toVars: any, position?: any): GSAPTimeline;
    set(target: any, vars: any, position?: any): GSAPTimeline;
    call(callback: (...args: any[]) => void, params?: any[], position?: any): GSAPTimeline;
    staggerTo(targets: any[], duration: number, vars: any, stagger: number, position?: any): GSAPTimeline;
    staggerFrom(targets: any[], duration: number, vars: any, stagger: number, position?: any): GSAPTimeline;
    staggerFromTo(
      targets: any[],
      duration: number,
      fromVars: any,
      toVars: any,
      stagger: number,
      position?: any,
    ): GSAPTimeline;
  }

  export interface GSAPStatic {
    to(target: any, vars: any): GSAPTween;
    from(target: any, vars: any): GSAPTween;
    fromTo(target: any, fromVars: any, toVars: any): GSAPTween;
    set(target: any, vars: any): GSAPTween;
    timeline(vars?: any): GSAPTimeline;
    context(callback: () => void): GSAPContext;
    context(scope: any, callback: () => void): GSAPContext;
    registerPlugin(...plugins: any[]): void;
  }

  const gsap: GSAPStatic;
  export default gsap;
}
