import { getPointLeaderboard } from "@/services/api";
import { LeaderboardResponse } from "@/types";
import { createSelectors } from "@/utils/create-selectors";
import { create } from "zustand";

interface LeaderboardState {
  data: LeaderboardResponse | null;
  loading: boolean;
  currentPage: number;
  fetchLeaderboard: (jwt: string, page?: number, take?: number) => Promise<void>;
  setCurrentPage: (page: number) => void;
}

const useLeaderboardStore = create<LeaderboardState>((set) => ({
  data: null,
  loading: false,
  currentPage: 1,

  fetchLeaderboard: async (jwt: string, page = 1, take = 10) => {
    set({ loading: true });
    try {
      const response = await getPointLeaderboard(jwt, page, take);

      if (response?.data && response?.meta) {
        set({
          data: {
            data: response.data,
            meta: {
              page: response.meta.page,
              take: response.meta.take,
              itemCount: response.meta.itemCount,
              pageCount: response.meta.pageCount,
              hasPreviousPage: response.meta.hasPreviousPage,
              hasNextPage: response.meta.hasNextPage,
            },
          },
        });
      }
    } finally {
      set({ loading: false });
    }
  },

  setCurrentPage: (page: number) => set({ currentPage: page }),
}));

export const useLeaderboardSelector = createSelectors(useLeaderboardStore);
