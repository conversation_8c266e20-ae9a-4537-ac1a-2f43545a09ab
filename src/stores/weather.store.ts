import { IWeatherStore } from "@/types";
import { createSelectors } from "@/utils/create-selectors";
import { getWeatherIcon } from "@/utils/location";
import { getCurrentAddress } from "@/utils/location";
import { create } from "zustand";

import { displayTime, parseToTempFormat } from "../utils/index";

const useWeather = create<IWeatherStore>((set, get) => ({
  weatherInfo: {},
  loadingWeather: false,
  failedToFetchWeather: false,
  currentAddress: {},
  currentTime: displayTime(),

  updateTime: () => set({ currentTime: displayTime() }),

  fetchWeatherInfo: async () => {
    set({ loadingWeather: true });
    const address = await getCurrentAddress();
    if (!address) {
      set({ failedToFetchWeather: true, loadingWeather: false });
      return;
    }

    set({
      currentAddress: address,
      loadingWeather: false,
    });
  },

  setCurrentAddress: async () => {
    const address = await getCurrentAddress();
    if (!address) {
      return;
    }
    set({ currentAddress: address });
  },

  weatherIcon: () => {
    const { weatherInfo } = get();
    if (weatherInfo.current) {
      return getWeatherIcon();
    }
    return "";
  },

  temperature: () => {
    const temp = get().weatherInfo.current?.temperature_2m;
    return temp ? parseToTempFormat(temp) : "";
  },
}));

export const useWeatherSelector = createSelectors(useWeather);
