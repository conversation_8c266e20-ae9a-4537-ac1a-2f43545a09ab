import { ICodeStore } from "@/types";
import { createSelectors } from "@/utils/create-selectors";
import { create } from "zustand";

const useCodeStore = create<ICodeStore>((set) => ({
  code: "",
  failed: false,
  inviteCode: null,
  clearCode: () => set({ code: "" }),
  setFailed: (failed) => set({ failed }),
  handleOTPChange: (e) => {
    set({ code: e, failed: false });
  },
  setInviteCode: (inviteCode) => set({ inviteCode }),
}));

export const useCodeSelector = createSelectors(useCodeStore);
