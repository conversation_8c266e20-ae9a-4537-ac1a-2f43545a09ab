import { BiodiversityTerritory, ForestHistoryData, ForestHistoryHydroshed, Territory } from "@/types";
import { createSelectors } from "@/utils/create-selectors";
import { create } from "zustand";

export interface MapState {
  selectedTerritory: Territory | null;
  selectedPropertyDetails: any;
  selectedHydroshedId: number | null;
  forestHistory: ForestHistoryData | null;
  forestHistoryHydroshed: ForestHistoryHydroshed | null;
  isLoadingForestHistory: boolean;
  isLoadingForestHistoryHydroshed: boolean;
  selectedItem: {
    id: string | null;
    type: "biomes" | "ecoregions" | "voronoi" | null;
    properties: any;
  };
  zoom: number;
  padding: any;
  lat: number;
  lng: number;
  forestHistoryError: string | null;
  forestHistoryHydroshedError: string | null;
  isTransitioning: boolean;
  breadcrumbs: any[];
  biodiversityTerritories: BiodiversityTerritory | null;
  isLoadingBiodiversityTerritories: boolean;
  biodiversityTerritoriesError: string | null;
  reloadProperties: boolean;
  reloadHydrosheds: boolean;
  setSelectedTerritory: (territory: Territory | null) => void;
  setSelectedPropertyDetails: (details: any) => void;
  setSelectedHydroshedId: (id: number | null) => void;
  setForestHistory: (history: ForestHistoryData | null) => void;
  setIsLoadingForestHistory: (loading: boolean) => void;
  setForestHistoryHydroshed: (hydroshed: ForestHistoryHydroshed | null) => void;
  setIsLoadingForestHistoryHydroshed: (loading: boolean) => void;
  setSelectedItem: (item: {
    id: string | null;
    type: "biomes" | "ecoregions" | "voronoi" | null;
    properties: any;
  }) => void;
  setZoom: (zoom: number) => void;
  setPadding: (padding: any) => void;
  setLat: (lat: number) => void;
  setLng: (lng: number) => void;
  setForestHistoryError: (error: string | null) => void;
  setForestHistoryHydroshedError: (error: string | null) => void;
  setIsTransitioning: (value: boolean) => void;
  setBreadcrumbs: (value: any[] | ((prev: any[]) => any[])) => void;
  setBiodiversityTerritories: (territories: BiodiversityTerritory | null) => void;
  setIsLoadingBiodiversityTerritories: (loading: boolean) => void;
  setBiodiversityTerritoriesError: (error: string | null) => void;
  triggerPropertiesReload: () => void;
  triggerHydroshedsReload: () => void;
}

const useMapStore = create<MapState>((set) => ({
  selectedTerritory: null,
  selectedPropertyDetails: null,
  selectedHydroshedId: null,
  forestHistory: null,
  isLoadingForestHistory: false,
  forestHistoryHydroshed: null,
  isLoadingForestHistoryHydroshed: false,
  selectedItem: {
    id: null,
    type: null,
    properties: null,
  },
  biodiversityTerritories: null,
  isLoadingBiodiversityTerritories: false,
  biodiversityTerritoriesError: null,
  zoom: 2,
  padding: { top: 0, bottom: 0, left: 0, right: 100 },
  lat: -51.56549537910225,
  lng: -10.308346761970142,
  forestHistoryError: null,
  forestHistoryHydroshedError: null,
  isTransitioning: false,
  breadcrumbs: [],
  reloadProperties: false,
  reloadHydrosheds: false,
  setSelectedTerritory: (territory) => set({ selectedTerritory: territory }),
  setSelectedPropertyDetails: (details) => set({ selectedPropertyDetails: details }),
  setForestHistory: (history) => set({ forestHistory: history }),
  setIsLoadingForestHistory: (loading) => set({ isLoadingForestHistory: loading }),
  setSelectedItem: (item) => set({ selectedItem: item }),
  setZoom: (zoom) => set({ zoom }),
  setPadding: (padding) => set({ padding }),
  setLat: (lat) => set({ lat }),
  setLng: (lng) => set({ lng }),
  setForestHistoryError: (error) => set({ forestHistoryError: error }),
  setForestHistoryHydroshedError: (error) => set({ forestHistoryHydroshedError: error }),
  setSelectedHydroshedId: (id) => set({ selectedHydroshedId: id }),
  setForestHistoryHydroshed: (hydroshed) => set({ forestHistoryHydroshed: hydroshed }),
  setIsLoadingForestHistoryHydroshed: (loading) => set({ isLoadingForestHistoryHydroshed: loading }),
  setIsTransitioning: (value) => set({ isTransitioning: value }),
  setBreadcrumbs: (value) =>
    set((state) => ({
      breadcrumbs: typeof value === "function" ? value(state.breadcrumbs) : value,
    })),
  setBiodiversityTerritories: (territories) => set({ biodiversityTerritories: territories }),
  setIsLoadingBiodiversityTerritories: (loading) => set({ isLoadingBiodiversityTerritories: loading }),
  setBiodiversityTerritoriesError: (error) => set({ biodiversityTerritoriesError: error }),
  triggerPropertiesReload: () => set((state) => ({ reloadProperties: !state.reloadProperties })),
  triggerHydroshedsReload: () => set((state) => ({ reloadHydrosheds: !state.reloadHydrosheds })),
}));

export const useMapSelector = createSelectors(useMapStore);

export default useMapStore;
