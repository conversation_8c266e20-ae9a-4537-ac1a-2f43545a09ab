import { territoriesService } from "@/services/territories.service";
import { ClaimedProperties } from "@/types";
import { useQuery } from "@tanstack/react-query";

export function usePropertyDetails(id: string) {
  return useQuery({
    queryKey: ["property", id],
    queryFn: () => {
      if (!id) throw new Error("Property ID is required");

      // TODO: passed 0 to prevent build fail, fix it later.
      return territoriesService.getPropertyDetails(id.toString(), 0, 0);
    },
    enabled: !!id,
  });
}

export function useMapData({ enabled }: { enabled: { forest: boolean; alerts: boolean } }) {
  const forestData = useQuery({
    queryKey: ["forestData"],
    queryFn: () => territoriesService.getForestData(),
    enabled: enabled.forest,
  });

  const alertsData = useQuery({
    queryKey: ["alertsData"],
    queryFn: () => territoriesService.getAlertsData(),
    enabled: enabled.alerts,
  });

  return {
    forestData,
    alertsData,
  };
}

export function useClaimedProperties() {
  return useQuery<ClaimedProperties>({
    queryKey: ["claimedProperties"],
    queryFn: () => territoriesService.getClaimedProperties().then((res) => res.data),
  });
}
