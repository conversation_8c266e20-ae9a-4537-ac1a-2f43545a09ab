import { authService } from "@/services/auth.service";
import { IValidateCodeResponse } from "@/types";
import { handleStorage } from "@/utils/storage";
import { useMutation, useQueryClient } from "@tanstack/react-query";

interface ValidateCodeOptions {
  onSuccess?: (data: IValidateCodeResponse) => void;
  onError?: (error: Error) => void;
}

/**
 * Hook for validating invite codes using React Query
 * @param options Optional callbacks for success and error handling
 * @returns Mutation object for validating invite codes
 */
export const useValidateCodeMutation = (options?: ValidateCodeOptions) => {
  const queryClient = useQueryClient();

  return useMutation<IValidateCodeResponse, Error, string>({
    mutationFn: async (code: string) => {
      const response = await authService.validateCode(code);

      // If the response contains an error property, throw an error to trigger onError
      if (response.error) {
        throw new Error(response.error);
      }

      return response;
    },
    onSuccess: (data) => {
      // Invalidate the referralCode query to refetch the latest data
      queryClient.invalidateQueries({ queryKey: ["referralCode"] });

      // Remove the invite code from localStorage on successful validation
      handleStorage("local", "inviteCode", "remove");

      // Call the user-provided onSuccess callback if it exists
      if (options?.onSuccess) {
        options.onSuccess(data);
      }
    },
    onError: (error) => {
      console.error("Code validation failed:", error);

      // Call the user-provided onError callback if it exists
      if (options?.onError) {
        options.onError(error);
      }
    },
  });
};
