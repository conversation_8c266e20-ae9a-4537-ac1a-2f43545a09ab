import { useUserSelector } from "@/stores/user.store";
import { IUserStore } from "@/types";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

import usePrivyAuth from "./usePrivyAuth";
import { useToast } from "./useToast";

interface UseUserProps extends IUserStore {
  redirectUser(path: string): void;
}

export default function useUser(disableEffects?: boolean): UseUserProps {
  const router = useRouter();
  const { toast } = useToast();
  const { authenticated, ready } = usePrivyAuth();

  const userData = useUserSelector.use.userData();

  function redirectUser(path: string) {
    if (!authenticated && ready) {
      router.push(path);
      toast({ title: "User logged out" });
    }
  }

  useEffect(() => {
    if (!disableEffects && !authenticated && ready) {
      redirectUser("/");
    }
  }, [authenticated, ready]);

  return {
    userData,
    redirectUser,
  };
}
