import { PrivyAuthContextProps } from "@/types";
import { useContextSelector } from "use-context-selector";

import PrivyAuthContext from "../contexts/PrivyAuthContext";

/**
 * Hook to access PrivyAuth context.
 *
 * @return {Object} An object containing user, userData, setUserData, onLogin, and onLogout.
 */
export default function usePrivyAuth(): PrivyAuthContextProps {
  const user = useContextSelector(PrivyAuthContext, (auth) => auth.user);
  const wallets = useContextSelector(PrivyAuthContext, (auth) => auth.wallets);
  const ready = useContextSelector(PrivyAuthContext, (auth) => auth.ready);
  const authenticated = useContextSelector(PrivyAuthContext, (auth) => auth.authenticated);

  const onLogin = useContextSelector(PrivyAuthContext, (auth) => auth.onLogin);
  const onLogout = useContextSelector(PrivyAuthContext, (auth) => auth.onLogout);
  const confirmLogout = useContextSelector(PrivyAuthContext, (auth) => auth.confirmLogout);
  const onCreateWallet = useContextSelector(PrivyAuthContext, (auth) => auth.onCreateWallet);
  const getJWT = useContextSelector(PrivyAuthContext, (auth) => auth.getJWT);

  return {
    user,
    wallets,
    ready,
    authenticated,
    onLogin,
    onLogout,
    confirmLogout,
    onCreateWallet,
    getJWT,
  };
}
