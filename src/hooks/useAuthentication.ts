import { handleStorage } from "@/utils/storage";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import useCode from "./useCode";
import usePrivyAuth from "./usePrivyAuth";

/**
 * Hook to handle authentication flow and invite code validation
 * @returns Authentication state including authenticated status, ready state, and validation status
 */
const useAuthentication = () => {
  const router = useRouter();
  const pathname = usePathname();
  const { ready, authenticated } = usePrivyAuth();
  const { sendCode } = useCode();
  const [isValidating, setIsValidating] = useState(true);

  /** Performs initial authentication validations including invite code check */
  const handleValidations = async () => {
    if (!isValidating) return;

    const inviteCode = handleStorage<string>("local", "inviteCode", "get");
    if (inviteCode) {
      // Use the sendCode function from useCode hook to validate the invite code
      await sendCode();

      // Redirect to world page if on drop page
      pathname === "/drop" && router.push("/claim");
      return;
    }

    // Redirect to world page if on drop page
    pathname === "/drop" && router.push("/claim");
    setIsValidating(false);
  };

  // Check authentication status when component is ready
  useEffect(() => {
    if (authenticated) {
      handleValidations();
    }
  }, [authenticated]);

  // Redirect to drop page when user is not authenticated
  useEffect(() => {
    if (ready && !authenticated) {
      router.push("/drop");
    }
  }, [ready, authenticated]);

  return { authenticated, ready, isValidating };
};

export default useAuthentication;
