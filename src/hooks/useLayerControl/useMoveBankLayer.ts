import { MOVEBANK_STUDY_ID } from "@/types/layer";
import { Map } from "mapbox-gl";
import { useCallback, useEffect, useState } from "react";

import { MovebankIndividual, MovebankLocation, MovebankStudy } from "../../types/movebank";

const getMapInstance = (map: Map | null): mapboxgl.Map | null => {
  if (!map) return null;

  try {
    return (map as any).getMap();
  } catch (error) {
    return map;
  }
};

const layerExists = (map: mapboxgl.Map | null, layerId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getLayer === "function" && map.getLayer(layerId) !== undefined;
  } catch (error) {
    return false;
  }
};

const sourceExists = (map: mapboxgl.Map | null, sourceId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getSource === "function" && map.getSource(sourceId) !== undefined;
  } catch (error) {
    return false;
  }
};

interface UseMoveBankLayerProps {
  map: Map | null;
  enabled: boolean;
  studyId?: number;
}

export const useMoveBankLayer = ({ map, enabled, studyId = MOVEBANK_STUDY_ID }: UseMoveBankLayerProps) => {
  const [studies, setStudies] = useState<MovebankStudy[]>([]);
  const [individuals, setIndividuals] = useState<MovebankIndividual[]>([]);
  const [locations, setLocations] = useState<MovebankLocation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const sourceId = "movebank-source";
  const layerId = "movebank-layer";

  const fetchFromApi = useCallback(async (endpoint: string, queryParams: Record<string, string> = {}): Promise<any> => {
    try {
      const queryString = new URLSearchParams(queryParams).toString();
      const url = `/api/movebank/${endpoint}${queryString ? `?${queryString}` : ""}`;
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error fetching from Movebank API:", error);
      throw error;
    }
  }, []);

  const fetchStudies = useCallback(async (): Promise<MovebankStudy[]> => {
    try {
      const result = await fetchFromApi("study", {});
      return Array.isArray(result) ? result : [];
    } catch (error) {
      console.error("Error fetching studies:", error);
      return [];
    }
  }, [fetchFromApi]);

  const fetchAnimalData = useCallback(
    async (studyId: number) => {
      const params = {
        study_id: studyId.toString(),
        sensor_type: "gps",
      };

      try {
        const data = await fetchFromApi("individual", params);
        const extractedIndividuals: MovebankIndividual[] = [];
        const extractedLocations: MovebankLocation[] = [];

        if (data?.individuals?.length) {
          data.individuals.forEach((individual: any) => {
            extractedIndividuals.push({
              id: individual.individual_id,
              study_id: individual.study_id,
              local_identifier: individual.individual_local_identifier,
              taxon_canonical_name: individual.individual_taxon_canonical_name || "Unknown",
            });

            if (individual.locations?.length) {
              individual.locations.forEach((location: any) => {
                if (location.location_lat && location.location_long) {
                  extractedLocations.push({
                    event_id: location.timestamp || Date.now(),
                    individual_id: individual.individual_id,
                    study_id: individual.study_id,
                    timestamp: location.timestamp
                      ? new Date(location.timestamp).toISOString()
                      : new Date().toISOString(),
                    location_lat: location.location_lat,
                    location_long: location.location_long,
                  });
                }
              });
            }
          });
        }

        return { individuals: extractedIndividuals, locations: extractedLocations };
      } catch (error) {
        console.error("Error fetching animal data:", error);
        throw error;
      }
    },
    [fetchFromApi],
  );

  const createGeoJSON = useCallback((locations: MovebankLocation[]) => {
    return {
      type: "FeatureCollection",
      features: locations.map((location) => ({
        type: "Feature",
        geometry: {
          type: "Point",
          coordinates: [location.location_long, location.location_lat],
        },
        properties: {
          id: location.event_id,
          individual_id: location.individual_id,
          timestamp: location.timestamp,
        },
      })),
    } as GeoJSON.FeatureCollection;
  }, []);

  useEffect(() => {
    const loadData = async () => {
      if (!enabled) {
        setStudies([]);
        setIndividuals([]);
        setLocations([]);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const [studiesData, animalData] = await Promise.all([fetchStudies(), fetchAnimalData(studyId)]);

        setStudies(studiesData);
        setIndividuals(animalData.individuals);
        setLocations(animalData.locations);
      } catch (err) {
        setError(err instanceof Error ? err : new Error("Failed to fetch Movebank data"));
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [enabled, studyId, fetchStudies, fetchAnimalData]);

  useEffect(() => {
    if (!map) return;

    const mapInstance = getMapInstance(map);
    if (!mapInstance) return;

    if (enabled && locations.length > 0) {
      const geojsonData = createGeoJSON(locations);

      const hasSource = sourceExists(mapInstance, sourceId);

      if (!hasSource) {
        try {
          mapInstance.addSource(sourceId, {
            type: "geojson",
            data: geojsonData,
          });
        } catch (error) {
          console.error("Error adding movebank source:", error);
          return;
        }
      } else {
        try {
          (mapInstance.getSource(sourceId) as mapboxgl.GeoJSONSource).setData(geojsonData);
        } catch (error) {
          console.error("Error updating movebank source data:", error);
          return;
        }
      }

      const hasLayer = layerExists(mapInstance, layerId);

      if (!hasLayer) {
        try {
          mapInstance.addLayer({
            id: layerId,
            type: "circle",
            source: sourceId,
            paint: {
              "circle-radius": 4,
              "circle-color": "#FF0000",
              "circle-opacity": 0.7,
            },
          });
        } catch (error) {
          console.error("Error adding movebank layer:", error);
        }
      }
    } else {
      if (layerExists(mapInstance, layerId)) {
        try {
          mapInstance.removeLayer(layerId);
        } catch (error) {
          console.error("Error removing movebank layer:", error);
        }
      }

      if (sourceExists(mapInstance, sourceId)) {
        try {
          mapInstance.removeSource(sourceId);
        } catch (error) {
          console.error("Error removing movebank source:", error);
        }
      }
    }

    return () => {
      if (map) {
        const mapInstance = getMapInstance(map);
        if (!mapInstance) return;

        if (layerExists(mapInstance, layerId)) {
          try {
            mapInstance.removeLayer(layerId);
          } catch (error) {
            console.error("Error removing movebank layer in cleanup:", error);
          }
        }

        if (sourceExists(mapInstance, sourceId)) {
          try {
            mapInstance.removeSource(sourceId);
          } catch (error) {
            console.error("Error removing movebank source in cleanup:", error);
          }
        }
      }
    };
  }, [map, enabled, locations, createGeoJSON]);

  return {
    studies,
    individuals,
    locations,
    isLoading,
    error,
  };
};

export default useMoveBankLayer;
