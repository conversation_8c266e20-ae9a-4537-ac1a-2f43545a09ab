import { OPENWEATHER_API_KEY } from "@/utils/constants";
import { Map } from "mapbox-gl";
import { useEffect, useRef, useState } from "react";

interface UseOpenWeatherLayersProps {
  map: Map | null;
  layerId: "wind" | "precipitation" | "clouds" | "temperature" | "pressure";
  enabled: boolean;
}

const LAYER_CONFIGS = {
  wind: { urlPath: "wind_new", opacity: 0.7 },
  precipitation: { urlPath: "precipitation_new", opacity: 0.7 },
  clouds: { urlPath: "clouds_new", opacity: 0.6 },
  temperature: { urlPath: "temp_new", opacity: 0.65 },
  pressure: { urlPath: "pressure_new", opacity: 0.6 },
};

export const useOpenWeatherLayer = ({ map, layerId, enabled }: UseOpenWeatherLayersProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const sourceId = `openweather-${layerId}`;
  const mapLayerId = `openweather-${layerId}-layer`;
  const layerConfig = LAYER_CONFIGS[layerId];
  const url = `https://tile.openweathermap.org/map/${layerConfig.urlPath}/{z}/{x}/{y}.png?appid=${OPENWEATHER_API_KEY}`;
  const layerAddedRef = useRef(false);

  useEffect(() => {
    if (!map) return;

    const addLayer = () => {
      if (!map.getSource(sourceId)) {
        map.addSource(sourceId, {
          type: "raster",
          tiles: [url],
          tileSize: 256,
        });
      }

      if (!map.getLayer(mapLayerId)) {
        map.addLayer({
          id: mapLayerId,
          type: "raster",
          source: sourceId,
          paint: {
            "raster-opacity": layerConfig.opacity,
          },
        });
        layerAddedRef.current = true;
      }
    };

    const removeLayer = () => {
      if (map.getLayer(mapLayerId)) {
        map.removeLayer(mapLayerId);
      }
      if (map.getSource(sourceId)) {
        map.removeSource(sourceId);
      }
      layerAddedRef.current = false;
    };

    if (enabled && !layerAddedRef.current) {
      setIsLoading(true);
      try {
        addLayer();
      } catch (error) {
        console.error(`Error adding ${layerId} layer:`, error);
      } finally {
        setIsLoading(false);
      }
    } else if (!enabled && layerAddedRef.current) {
      removeLayer();
    }

    return () => {
      if (layerAddedRef.current) {
        removeLayer();
      }
    };
  }, [map, enabled, layerId, sourceId, mapLayerId, url, layerConfig.opacity]);

  return { isLoading };
};

export const useOpenWeatherLayers = (map: Map | null, enabledLayers: string[]) => {
  const windLayer = useOpenWeatherLayer({
    map,
    layerId: "wind",
    enabled: enabledLayers.includes("wind"),
  });

  const precipitationLayer = useOpenWeatherLayer({
    map,
    layerId: "precipitation",
    enabled: enabledLayers.includes("precipitation"),
  });

  const cloudsLayer = useOpenWeatherLayer({
    map,
    layerId: "clouds",
    enabled: enabledLayers.includes("clouds"),
  });

  const temperatureLayer = useOpenWeatherLayer({
    map,
    layerId: "temperature",
    enabled: enabledLayers.includes("temperature"),
  });

  const pressureLayer = useOpenWeatherLayer({
    map,
    layerId: "pressure",
    enabled: enabledLayers.includes("pressure"),
  });

  const isLoading =
    windLayer.isLoading ||
    precipitationLayer.isLoading ||
    cloudsLayer.isLoading ||
    temperatureLayer.isLoading ||
    pressureLayer.isLoading;

  return { isLoading };
};
