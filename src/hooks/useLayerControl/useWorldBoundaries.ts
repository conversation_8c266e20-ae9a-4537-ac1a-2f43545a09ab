import { getCountriesBoundariesData, getStatesFromCountryBoundariesData } from "@/services/api";
import { useMapSelector } from "@/stores/map.store";
import { INITIAL_COUNTRY_ZOOM } from "@/utils/constants";
import { compactName } from "@/utils/formatting";
import * as turf from "@turf/turf";
import { LngLatBounds, Map as MapboxMap } from "mapbox-gl";
import { useCallback, useRef, useState } from "react";

import useMap from "../useMap";
import usePrivyAuth from "../usePrivyAuth";
import { usePropertyManagementStore } from "./useProperties/store";

const layerExists = (map: MapboxMap | null, layerId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getLayer === "function" && map.getLayer(layerId) !== undefined;
  } catch (error) {
    return false;
  }
};

const sourceExists = (map: MapboxMap | null, sourceId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getSource === "function" && map.getSource(sourceId) !== undefined;
  } catch (error) {
    return false;
  }
};

const safeQueryRenderedFeatures = (
  map: MapboxMap,
  point: mapboxgl.Point,
  options: { layers: string[] },
): mapboxgl.MapboxGeoJSONFeature[] => {
  try {
    const allLayersExist = options.layers.every((layerId) => layerExists(map, layerId));
    if (!allLayersExist) return [];

    return map.queryRenderedFeatures(point, options);
  } catch (error) {
    console.error("Error querying rendered features:", error);
    return [];
  }
};

interface BoundaryProperties {
  id: string;
  name: string;
  externalId?: string;
  type: "country" | "state";
  areaHa?: number;
}

interface BoundaryClickInfo {
  id: string;
  name: string;
  type: "country" | "state" | null;
  country: string | null;
}

interface WorldBoundariesProps {
  onClick?: (properties: BoundaryClickInfo | null) => void;
  maxZoomForFill?: number;
}

export interface ExtendedMap extends MapboxMap {
  _boundaryHandlers?: {
    handleCountryHover: (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => void;
    handleStateHover: (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => void;
    handleCountryClick: (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => void;
    handleStateClick: (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => void;
    handleLeave: (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => void;
    handleZoomChange: () => void;
  };
}

const dataCache = {
  countries: null as any | null,
  states: {} as Record<string, any>,
};

export const useWorldBoundaries = ({ onClick, maxZoomForFill = 7 }: WorldBoundariesProps = {}) => {
  const selectedCountryRef = useRef<string | null>(null);
  const selectedStateRef = useRef<{ id: number; feature: mapboxgl.MapboxGeoJSONFeature } | null>(null);
  const { getJWT } = usePrivyAuth();
  const [isLoadingCountries, setIsLoadingCountries] = useState(false);
  const [isLoadingStates, setIsLoadingStates] = useState(false);
  const lastZoomRef = useRef<number>(0);
  const { setShowProperties } = useMap();
  const setSelectedTerritory = useMapSelector.use.setSelectedTerritory();
  const setBreadcrumbs = useMapSelector.use.setBreadcrumbs();

  const { setSelectedPropertyDetails } = usePropertyManagementStore();

  const findCountryCoordinates = useCallback(
    async (countryName: string, map: ExtendedMap) => {
      try {
        await loadCountriesData();

        const country = dataCache.countries?.features?.find((feature) => {
          const featureNameFormatted = compactName(feature.properties?.name);
          const countryNameFormatted = compactName(countryName);

          return compactName(featureNameFormatted)?.includes(countryNameFormatted);
        });

        if (country && country.geometry) {
          const bbox = turf.bbox(country as any);
          const polygon = turf.bboxPolygon(bbox);
          const centroid = turf.centroid(polygon);
          const [longitude, latitude] = centroid.geometry.coordinates;

          map?.easeTo({
            center: [longitude, latitude],
            zoom: INITIAL_COUNTRY_ZOOM,
          });

          return {
            latitude,
            longitude,
            zoom: INITIAL_COUNTRY_ZOOM,
          };
        }

        map?.easeTo({
          center: [-51.419, -14.72],
          zoom: INITIAL_COUNTRY_ZOOM,
        });
      } catch (error) {
        map?.easeTo({
          center: [-51.419, -14.72],
          zoom: INITIAL_COUNTRY_ZOOM,
        });
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [getJWT],
  );

  const loadCountriesData = useCallback(async () => {
    if (dataCache.countries) {
      return dataCache.countries;
    }

    try {
      setIsLoadingCountries(true);
      const jwt = await getJWT();
      const data = await getCountriesBoundariesData(jwt as string);
      dataCache.countries = data;
      return data;
    } catch (error) {
      console.error("Error loading countries data:", error);
      return null;
    } finally {
      setIsLoadingCountries(false);
    }
  }, [getJWT]);

  const loadStatesForCountry = useCallback(
    async (countryId: string) => {
      if (dataCache.states[countryId]) {
        return dataCache.states[countryId];
      }

      try {
        setIsLoadingStates(true);
        const jwt = await getJWT();
        const data = await getStatesFromCountryBoundariesData(jwt as string, countryId);
        dataCache.states[countryId] = data;
        return data;
      } catch (error) {
        console.error(`Error loading states for country ${countryId}:`, error);
        return {
          type: "FeatureCollection",
          features: [],
        };
      } finally {
        setIsLoadingStates(false);
      }
    },
    [getJWT],
  );

  const createMask = useCallback(
    (feature: mapboxgl.MapboxGeoJSONFeature, type: "country" | "state" = "country", map?: mapboxgl.Map) => {
      const worldBbox = [-180, -90, 180, 90];
      const worldPoly = turf.bboxPolygon(worldBbox as any);

      if (!map) return worldPoly;

      const geometries: any[] = [];
      const properties = feature.properties;

      const source = type === "state" ? "states" : "countries";
      let sourceData: any = null;
      try {
        if (sourceExists(map, source)) {
          sourceData = (map.getSource(source) as mapboxgl.GeoJSONSource as any)?._data;
        }
      } catch (error) {
        console.error(`Error getting source data for ${source}:`, error);
      }

      if (sourceData?.features) {
        sourceData.features.forEach((feat: any) => {
          const isMatch =
            type === "state"
              ? feat.properties.type === "state" && feat.properties.id === properties?.id
              : feat.properties.type === "country" && feat.properties.id === properties?.id;

          if (isMatch) {
            const geom = feat.geometry;
            if (geom?.type === "MultiPolygon") {
              geometries.push(...geom.coordinates);
            } else if (geom?.type === "Polygon") {
              geometries.push(geom.coordinates);
            }
          }
        });
      }

      if (geometries.length > 0) {
        const poly = turf.multiPolygon(geometries);
        const features = turf.featureCollection([worldPoly, poly as any]);
        return turf.difference(features as any);
      }

      return worldPoly;
    },
    [],
  );

  const clearSelections = useCallback(
    (map: MapboxMap) => {
      setSelectedTerritory(null);
      setBreadcrumbs([]);

      try {
        if (sourceExists(map, "countries")) {
          const countriesFeatures = map.querySourceFeatures("countries");
          countriesFeatures.forEach((countryFeature) => {
            if (countryFeature.id !== undefined) {
              map.setFeatureState(
                { source: "countries", id: countryFeature.id },
                { stateSelected: false, selected: false, hover: false },
              );
            }
          });
        }

        if (sourceExists(map, "states")) {
          const statesFeatures = map.querySourceFeatures("states");
          statesFeatures.forEach((feature) => {
            if (feature.id !== undefined) {
              map.setFeatureState({ source: "states", id: feature.id }, { selected: false, hover: false });
            }
          });

          if (sourceExists(map, "states")) {
            const statesSource = map.getSource("states") as mapboxgl.GeoJSONSource;
            statesSource.setData({
              type: "FeatureCollection",
              features: [],
            });
          }
        }

        selectedCountryRef.current = null;
        selectedStateRef.current = null;

        if (sourceExists(map, "boundaries-mask")) {
          const maskSource = map.getSource("boundaries-mask") as mapboxgl.GeoJSONSource;
          maskSource.setData({
            type: "Feature",
            geometry: { type: "Polygon", coordinates: [] },
            properties: {},
          });
        }
      } catch (error) {
        console.error("Error clearing selections:", error);
      }

      onClick?.(null);
    },
    [onClick, setBreadcrumbs, setSelectedTerritory],
  );

  const updateSelection = useCallback(
    async (feature: mapboxgl.MapboxGeoJSONFeature, source: string, map: MapboxMap) => {
      if (selectedStateRef.current) {
        map.setFeatureState({ source: "states", id: selectedStateRef.current.id }, { selected: false, hover: false });
      }

      const previousCountryId = selectedCountryRef.current;
      let previousCountryName = null;

      try {
        if (sourceExists(map, "countries")) {
          const countriesFeatures = map.querySourceFeatures("countries");
          countriesFeatures.forEach((countryFeature) => {
            if (countryFeature.id !== undefined) {
              if (
                previousCountryId &&
                countryFeature.properties &&
                countryFeature.properties.id === previousCountryId
              ) {
                previousCountryName = countryFeature.properties.name;
              }
              map.setFeatureState(
                { source: "countries", id: countryFeature.id },
                { stateSelected: false, selected: false, hover: false },
              );
            }
          });
        }
      } catch (error) {
        console.error("Error updating selection:", error);
      }

      const properties = feature.properties as BoundaryProperties;

      if (feature.id !== undefined) {
        try {
          map.setFeatureState({ source, id: feature.id }, { selected: true });

          if (sourceExists(map, "countries")) {
            const countriesSource = map.getSource("countries") as mapboxgl.GeoJSONSource;
            const countriesData = (countriesSource as any)._data;
            const parentCountry = countriesData.features.find(
              (countryFeature: any) => countryFeature.properties.id === selectedCountryRef.current,
            );

            if (source === "states") {
              if (countriesData?.features) {
                if (parentCountry?.id !== undefined) {
                  selectedCountryRef.current = parentCountry.properties.id;

                  map.setFeatureState(
                    { source: "countries", id: parentCountry.id },
                    { stateSelected: true, selected: false },
                  );
                }
              }

              selectedStateRef.current = {
                id: feature.id as number,
                feature: feature,
              };
            } else {
              selectedCountryRef.current = properties.id;
              selectedStateRef.current = null;
            }
          }
        } catch (error) {
          console.error("Error setting feature state:", error);
        }
      }

      try {
        if (sourceExists(map, "boundaries-mask")) {
          const maskSource = map.getSource("boundaries-mask") as mapboxgl.GeoJSONSource;
          const mask = createMask(feature, properties.type, map);
          if (mask) maskSource.setData(mask);
        }

        if (properties.type === "country") {
          const statesData = await loadStatesForCountry(properties?.externalId as string);
          if (statesData && sourceExists(map, "states")) {
            const filteredStates = {
              type: "FeatureCollection",
              features: (statesData as any).features
                .filter((feat: any) => feat.properties.type === "state")
                .map((feat: any, index) => ({ ...feat, id: index })),
            };

            const statesSource = map.getSource("states") as mapboxgl.GeoJSONSource;
            statesSource.setData(filteredStates as any);
          }
        }
      } catch (error) {
        console.error("Error updating mask or states:", error);
      }

      const clickInfo: BoundaryClickInfo = {
        id: properties.id,
        name: properties.name,
        type: properties.type,
        country: previousCountryName,
      };

      return clickInfo;
    },
    [createMask, loadStatesForCountry],
  );

  const zoomToFeature = useCallback(
    (feature: mapboxgl.MapboxGeoJSONFeature, map: MapboxMap, type: "country" | "state" = "country") => {
      if (!feature.geometry) return;

      const turfFeature = {
        type: "Feature",
        properties: {},
        geometry: feature.geometry,
      };

      const bbox = turf.bbox(turfFeature as any);
      const polygon = turf.bboxPolygon(bbox);
      const centroid = turf.centroid(polygon);

      // eslint-disable-next-line prefer-const
      let [lon, lat] = centroid.geometry.coordinates;

      const currentCenter = map.getCenter();
      const currentLon = currentCenter.lng;

      while (lon - currentLon > 180) lon -= 360;
      while (lon - currentLon < -180) lon += 360;

      const bounds = new LngLatBounds([bbox[0], bbox[1]], [bbox[2], bbox[3]]);

      if (type === "country") {
        map.fitBounds(bounds, {
          minZoom: 3,
          duration: 300,
          essential: true,
        });

        map.easeTo({
          minZoom: 3,
          center: [lon, lat],
          duration: 300,
          essential: true,
        });
      } else {
        map.fitBounds(bounds, {
          padding: { top: 100, bottom: 100, left: 100, right: 100 },
          zoom: 5,
          duration: 300,
          essential: true,
        });
      }
    },
    [],
  );

  const setupBoundaries = useCallback(
    async (map: ExtendedMap) => {
      if (!map || !map.loaded()) return;

      const handleZoomChange = () => {
        const zoom = map.getZoom();
        lastZoomRef.current = zoom;

        ["state-fills", "state-borders", "country-fills", "country-borders"].forEach((layerId) => {
          if (map.getLayer(layerId)) {
            const opacityProperty = layerId.includes("borders") ? "line-opacity" : "fill-opacity";

            if (layerId.includes("borders")) {
              map.setPaintProperty(
                layerId,
                opacityProperty,
                layerId.includes("borders")
                  ? [
                      "case",
                      ["boolean", ["feature-state", "selected"], false],
                      1,
                      ["boolean", ["feature-state", "hover"], false],
                      0.7,
                      0.8,
                    ]
                  : [
                      "case",
                      ["boolean", ["feature-state", "selected"], false],
                      0,
                      ["boolean", ["feature-state", "hover"], false],
                      0.1,
                      0,
                    ],
              );
            }
          }
        });
      };

      const initializeBoundaries = async () => {
        try {
          setIsLoadingCountries(true);

          const data: any = await loadCountriesData();

          map.addSource("boundaries-mask", {
            type: "geojson",
            data: {
              type: "Feature",
              geometry: {
                type: "Polygon",
                coordinates: [],
              },
              properties: {},
            },
          });

          map.addSource("countries", {
            type: "geojson",
            data: {
              type: "FeatureCollection",
              features: [],
            },
            generateId: true,
          });

          map.addLayer({
            id: "country-fills",
            type: "fill",
            source: "countries",
            paint: {
              "fill-color": "#FFFFFF",
              "fill-opacity": [
                "case",
                ["boolean", ["feature-state", "selected"], false],
                0,
                ["boolean", ["feature-state", "hover"], false],
                0.1,
                0,
              ],
            },
          });

          map.addLayer({
            id: "country-borders",
            type: "line",
            source: "countries",
            paint: {
              "line-color": "#FFFFFF",
              "line-width": ["case", ["boolean", ["feature-state", "selected"], false], 3, 0.5],
              "line-opacity": ["case", ["boolean", ["feature-state", "selected"], false], 1, 0.8],
            },
          });

          map.addLayer({
            id: "boundaries-mask",
            type: "fill",
            source: "boundaries-mask",
            paint: {
              "fill-color": "#000000",
              "fill-opacity": 0.35,
            },
          });

          map.addSource("states", {
            type: "geojson",
            data: {
              type: "FeatureCollection",
              features: [],
            },
            generateId: true,
          });

          map.addLayer({
            id: "state-fills",
            type: "fill",
            source: "states",
            paint: {
              "fill-color": "#FFFFFF",
              "fill-opacity": [
                "case",
                ["boolean", ["feature-state", "selected"], false],
                0,
                ["boolean", ["feature-state", "hover"], false],
                0.1,
                0,
              ],
            },
          });

          map.addLayer({
            id: "state-borders",
            type: "line",
            source: "states",
            paint: {
              "line-color": "#FFFFFF",
              "line-width": [
                "case",
                ["boolean", ["feature-state", "selected"], false],
                2.5,
                ["boolean", ["feature-state", "hover"], false],
                2.5,
                0,
              ],
              "line-opacity": [
                "case",
                ["boolean", ["feature-state", "selected"], false],
                1,
                ["boolean", ["feature-state", "hover"], false],
                0.7,
                0.2,
              ],
            },
          });

          const countriesSource = map.getSource("countries") as mapboxgl.GeoJSONSource;
          if (countriesSource) {
            countriesSource.setData(data);
          }

          let hoveredCountryId: number | null = null;
          let hoveredStateId: number | null = null;

          const handleCountryHover = (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => {
            if (!e.features?.length || e.features[0].id === undefined) return;

            // Safely query rendered features
            const landUnitFeatures = safeQueryRenderedFeatures(map, e.point, {
              layers: ["properties"],
            });

            if (landUnitFeatures.length > 0) {
              if (hoveredCountryId !== null) {
                map.setFeatureState({ source: "countries", id: hoveredCountryId }, { hover: false });
                hoveredCountryId = null;
              }
              map.getCanvas().style.cursor = "pointer";
              return;
            }

            const feature = e.features[0];
            const properties = feature.properties as BoundaryProperties;

            if (selectedStateRef.current) {
              const stateFeature = selectedStateRef.current.feature;
              const stateProperties = stateFeature.properties as BoundaryProperties;

              if (properties.id === stateProperties.id.split("-")[0]) {
                map.getCanvas().style.cursor = "pointer";
                return;
              }
            }

            if (hoveredCountryId !== null && hoveredCountryId !== feature.id) {
              map.setFeatureState({ source: "countries", id: hoveredCountryId }, { hover: false });
              hoveredCountryId = null;
            }

            if (selectedCountryRef.current && properties.id === selectedCountryRef.current) {
              map.getCanvas().style.cursor = "pointer";
              return;
            }

            map.getCanvas().style.cursor = "pointer";

            hoveredCountryId = feature.id as number;
            map.setFeatureState({ source: "countries", id: hoveredCountryId }, { hover: true });
          };

          const handleStateHover = (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => {
            if (!e.features?.length || e.features[0].id === undefined) return;

            const landUnitFeatures = safeQueryRenderedFeatures(map, e.point, {
              layers: ["properties"],
            });

            if (landUnitFeatures.length > 0) {
              if (hoveredStateId !== null) {
                map.setFeatureState({ source: "states", id: hoveredStateId }, { hover: false });
                hoveredStateId = null;
              }
              map.getCanvas().style.cursor = "pointer";
              return;
            }

            const feature = e.features[0];

            map.getCanvas().style.cursor = "pointer";

            if (hoveredStateId !== null && hoveredStateId !== feature.id) {
              map.setFeatureState({ source: "states", id: hoveredStateId }, { hover: false });
            }

            hoveredStateId = feature.id as number;
            map.setFeatureState({ source: "states", id: hoveredStateId }, { hover: true });
          };

          const handleCountryClick = (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => {
            setShowProperties(true);
            const propertyFeatures = safeQueryRenderedFeatures(map, e.point, {
              layers: ["properties"],
            });

            if (propertyFeatures.length > 0) return;

            map.easeTo({
              pitch: 0,
              duration: 400,
            });

            const stateFeatures = safeQueryRenderedFeatures(map, e.point, {
              layers: ["state-fills"],
            });

            if (stateFeatures.length > 0) return;

            if (e.features?.length) {
              const feature = e.features[0];
              const properties = feature.properties as BoundaryProperties;

              if (properties.type === "country") {
                updateSelection(feature, "countries", map).then((clickInfo) => {
                  onClick?.(clickInfo);
                });

                zoomToFeature(feature, map, "country");
              }
            }

            setSelectedPropertyDetails(null);
          };

          const handleStateClick = (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => {
            setShowProperties(true);
            const propertyFeatures = safeQueryRenderedFeatures(map, e.point, {
              layers: ["properties"],
            });

            if (propertyFeatures.length > 0) return;

            if (e.features?.length) {
              e.preventDefault();
              const feature = e.features[0];
              const properties = feature.properties as BoundaryProperties;

              if (properties.type === "state") {
                updateSelection(feature, "states", map).then((clickInfo) => {
                  onClick?.(clickInfo);
                });

                zoomToFeature(feature, map, "state");
              }
            }

            setSelectedPropertyDetails(null);
          };

          const handleLeave = () => {
            if (hoveredStateId !== null) {
              map.setFeatureState({ source: "states", id: hoveredStateId }, { hover: false });
              hoveredStateId = null;
            }
            if (hoveredCountryId !== null) {
              map.setFeatureState({ source: "countries", id: hoveredCountryId }, { hover: false });
              hoveredCountryId = null;
            }
            map.getCanvas().style.cursor = "";
          };

          if (layerExists(map, "properties") && layerExists(map, "state-fills")) {
            try {
              map.moveLayer("properties", "state-fills");
            } catch (error) {
              console.error("Error moving properties layer:", error);
            }
          }

          if (layerExists(map, "property-outline") && layerExists(map, "properties")) {
            try {
              map.moveLayer("property-outline", "properties");
            } catch (error) {
              console.error("Error moving property-outline layer:", error);
            }
          }
          map.on("mousemove", "country-fills", handleCountryHover);
          map.on("mousemove", "state-fills", handleStateHover);
          map.on("mouseleave", "country-fills", handleLeave);
          map.on("mouseleave", "state-fills", handleLeave);
          map.on("click", "country-fills", handleCountryClick);
          map.on("click", "state-fills", handleStateClick);

          map.on("click", (e) => {
            const features = safeQueryRenderedFeatures(map, e.point, {
              layers: ["country-fills", "state-fills"],
            });

            if (features.length === 0) {
              clearSelections(map);
            }
          });

          map._boundaryHandlers = {
            handleCountryHover,
            handleStateHover,
            handleCountryClick,
            handleStateClick,
            handleLeave,
            handleZoomChange,
          };

          handleZoomChange();
        } catch (error) {
          console.error("Error setting up boundaries:", error);
        } finally {
          setIsLoadingCountries(false);
        }
      };

      await initializeBoundaries();
      map.on("style.load", handleZoomChange);

      map.on("style.load", async () => {
        await initializeBoundaries();
      });

      return () => {
        map.off("style.load", handleZoomChange);
      };
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      clearSelections,
      createMask,
      loadCountriesData,
      loadStatesForCountry,
      maxZoomForFill,
      onClick,
      updateSelection,
      zoomToFeature,
    ],
  );

  const cleanup = useCallback((map: ExtendedMap) => {
    if (!map) return;

    try {
      if (map._boundaryHandlers) {
        map.off("mousemove", "country-fills", map._boundaryHandlers.handleCountryHover);
        map.off("mousemove", "state-fills", map._boundaryHandlers.handleStateHover);
        map.off("mouseleave", "country-fills", map._boundaryHandlers.handleLeave);
        map.off("mouseleave", "state-fills", map._boundaryHandlers.handleLeave);
        map.off("click", "country-fills", map._boundaryHandlers.handleCountryClick);
        map.off("click", "state-fills", map._boundaryHandlers.handleStateClick);
        map.off("zoom", map._boundaryHandlers.handleZoomChange);

        delete map._boundaryHandlers;
      }

      ["boundaries-mask", "country-borders", "country-fills", "state-borders", "state-fills"].forEach((layerId) => {
        if (layerExists(map, layerId)) {
          try {
            map.removeLayer(layerId);
          } catch (error) {
            console.error(`Error removing layer ${layerId}:`, error);
          }
        }
      });

      ["boundaries-mask", "countries", "states"].forEach((sourceId) => {
        if (sourceExists(map, sourceId)) {
          try {
            map.removeSource(sourceId);
          } catch (error) {
            console.error(`Error removing source ${sourceId}:`, error);
          }
        }
      });
    } catch (error) {
      console.error("Error cleaning up boundaries:", error);
    }
  }, []);

  return {
    setupBoundaries,
    cleanup,
    isLoadingCountries,
    isLoadingStates,
    findCountryCoordinates,
    loadCountriesData,
    loadStatesForCountry,
    createMask,
    clearSelections,
    updateSelection,
    zoomToFeature,
    getCountriesData: () => dataCache.countries,
    getStatesData: (countryId: string) => dataCache.states[countryId],
  };
};

export default useWorldBoundaries;
