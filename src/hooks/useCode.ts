"use client";

import usePrivyAuth from "@/hooks/usePrivyAuth";
import { useToast } from "@/hooks/useToast";
import { useAuth } from "@/queries/hooks/useAuth";
import { useValidateCodeMutation } from "@/queries/mutations/auth.mutations";
import { useCodeSelector } from "@/stores/code.store";
import { ICodeStore, IValidateCodeResponse } from "@/types";
import { handleStorage } from "@/utils/storage";
import { useEffect } from "react";

interface UseCodeProps extends Omit<ICodeStore, "setInviteCode"> {
  sendCode: () => Promise<void>;
  submitCode: (code: string) => Promise<IValidateCodeResponse>;
  isValidating: boolean;
}

/**
 * Hook for managing invite code validation and state
 * @returns Functions and state for invite code management
 */
export default function useCode(): UseCodeProps {
  const { confirmLogout } = usePrivyAuth();
  const { toast } = useToast();

  // Use React Query for fetching referral code
  const { referralCode } = useAuth() || { referralCode: undefined };

  // Get state from the code store
  const code = useCodeSelector.use.code();
  const failed = useCodeSelector.use.failed();
  const setFailed = useCodeSelector.use.setFailed();
  const handleOTPChange = useCodeSelector.use.handleOTPChange();
  const clearCode = useCodeSelector.use.clearCode();
  const inviteCode = useCodeSelector.use.inviteCode();
  const setInviteCode = useCodeSelector.use.setInviteCode();

  // Set invite code in the store when it changes
  useEffect(() => {
    if (referralCode?.data?.data) {
      // Ensure the data has the expected structure
      const inviteCodeData = referralCode.data.data;

      // If uses is undefined, add an empty array
      if (!inviteCodeData.uses) {
        inviteCodeData.uses = [];
      }

      // Store the single invite code
      setInviteCode(inviteCodeData);
    }
  }, [referralCode?.data, setInviteCode]);

  // Use the React Query mutation for validating invite codes
  const validateCodeMutation = useValidateCodeMutation({
    onSuccess: (data) => {
      clearCode();
      toast({
        title: "Success",
        description: data.message || "Invite code validated successfully",
      });
    },
    onError: (error) => {
      setFailed(true);
      clearCode();
      toast({
        title: "Error",
        description: error.message || "Failed to validate invite code",
      });

      // Only log out if there was an authentication error
      if (error.message.includes("authentication") || error.message.includes("unauthorized")) {
        confirmLogout(true);
      }
    },
  });

  /**
   * Validates a stored invite code from localStorage
   */
  async function sendCode() {
    const storedCode = handleStorage<string>("local", "inviteCode", "get");
    if (!storedCode) return;

    try {
      await validateCodeMutation.mutateAsync(storedCode);
    } catch (error) {
      // Error is handled in the onError callback
    }
  }

  /**
   * Submits an invite code for validation
   * @param code The invite code to validate
   * @returns A response object with success status and message
   */
  const submitCode = async (code: string): Promise<IValidateCodeResponse> => {
    try {
      const result = await validateCodeMutation.mutateAsync(code);
      return {
        success: true,
        message: result.message || "Code validated successfully",
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to validate code",
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  };

  return {
    sendCode,
    submitCode,
    code,
    failed,
    inviteCode:
      inviteCode ||
      (referralCode?.data
        ? {
            ...referralCode.data,
            // Ensure uses is always an array
            uses: referralCode.data.data.uses || [],
          }
        : null),
    setFailed,
    handleOTPChange,
    clearCode,
    isValidating: validateCodeMutation.isPending,
  };
}
