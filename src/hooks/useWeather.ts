import { useWeatherSelector } from "@/stores/weather.store";
import { IWeatherStore } from "@/types";
import { useEffect } from "react";

interface UseWeatherProps extends IWeatherStore {}

export default function useWeather(disableEffects?: boolean): UseWeatherProps {
  const loading = useWeatherSelector.use.loadingWeather();
  const failedToFetchWeather = useWeatherSelector.use.failedToFetchWeather();
  const currentAddress = useWeatherSelector.use.currentAddress();
  const weather = useWeatherSelector.use.weatherInfo();
  const currentTime = useWeatherSelector.use.currentTime();

  const fetchWeatherInfo = useWeatherSelector.use.fetchWeatherInfo();
  const setCurrentAddress = useWeatherSelector.use.setCurrentAddress();
  const weatherIcon = useWeatherSelector.use.weatherIcon();
  const temperature = useWeatherSelector.use.temperature();
  const updateTime = useWeatherSelector.use.updateTime();

  useEffect(() => {
    if (!disableEffects) fetchWeatherInfo();
  }, [disableEffects, fetchWeatherInfo]);

  useEffect(() => {
    if (!disableEffects) {
      const intervalId = setInterval(() => {
        updateTime();
      }, 1000);

      return () => clearInterval(intervalId);
    }
  }, [disableEffects, updateTime]);

  return {
    loadingWeather: loading,
    failedToFetchWeather,
    currentAddress,
    weatherInfo: weather,
    currentTime,
    fetchWeatherInfo,
    weatherIcon,
    temperature,
    setCurrentAddress,
    updateTime,
  };
}
