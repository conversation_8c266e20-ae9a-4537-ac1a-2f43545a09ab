import { useMapSelector } from "@/stores/map.store";
import { ForestHistoryData } from "@/types";
import { useCallback } from "react";

import usePrivyAuth from "./usePrivyAuth";

export const useForestHistoryProperties = () => {
  const { setForestHistory, setIsLoadingForestHistory, setForestHistoryError } = useMapSelector((state) => ({
    setForestHistory: state.setForestHistory,
    setIsLoadingForestHistory: state.setIsLoadingForestHistory,
    setForestHistoryError: state.setForestHistoryError,
  }));

  const { getJWT } = usePrivyAuth();

  const fetchPropertyForestHistory = useCallback(
    async (id: string) => {
      if (!id) return;

      try {
        setIsLoadingForestHistory(true);
        setForestHistoryError(null);

        const jwt = await getJWT();
        const response = await fetch(`https://api.yby.energy/api/properties/forest-history?id=${id}`, {
          headers: {
            Authorization: `Bearer ${jwt}`,
          },
        });

        if (!response.ok) {
          throw new Error("Failed to fetch property forest history");
        }

        const propertyData = await response.json();

        const transformedData: ForestHistoryData = {
          id: propertyData.id,
          forestHistory: propertyData.forestHistory,
          isPropertyData: true,
        };

        setForestHistory(transformedData);
      } catch (err) {
        console.error("Error fetching property forest history:", err);
        setForestHistoryError(err instanceof Error ? err.message : "Failed to fetch property forest history");
        setForestHistory(null);
      } finally {
        setIsLoadingForestHistory(false);
      }
    },
    [getJWT, setForestHistory, setForestHistoryError, setIsLoadingForestHistory],
  );

  return {
    fetchPropertyForestHistory,
  };
};
