import { useMapSelector } from "@/stores/map.store";
import { BiodiversityTerritory } from "@/types";

import usePrivyAuth from "./usePrivyAuth";

export const useBiodiversityTerritories = () => {
  const {
    setBiodiversityTerritories,
    setIsLoadingBiodiversityTerritories,
    setBiodiversityTerritoriesError,
    setSelectedTerritory,
  } = useMapSelector((state) => ({
    setBiodiversityTerritories: state.setBiodiversityTerritories,
    setIsLoadingBiodiversityTerritories: state.setIsLoadingBiodiversityTerritories,
    setBiodiversityTerritoriesError: state.setBiodiversityTerritoriesError,
    setSelectedTerritory: state.setSelectedTerritory,
  }));

  const { getJWT } = usePrivyAuth();

  const fetchBiodiversityTerritories = async (territoryId: string) => {
    if (!territoryId) return;

    try {
      setIsLoadingBiodiversityTerritories(true);
      setBiodiversityTerritoriesError(null);

      const jwt = await getJWT();
      const response = await fetch(
        `https://api.yby.energy/api/territories/v2/biodiversity?territoryId=${territoryId}`,
        {
          headers: {
            Authorization: `Bearer ${jwt}`,
          },
        },
      );

      if (!response.ok) {
        throw new Error("Failed to fetch forest history");
      }
      const data: BiodiversityTerritory = await response.json();
      setBiodiversityTerritories(data);
    } catch (err) {
      console.error("Error fetching biodiversity territories:", err);
      setBiodiversityTerritoriesError(err instanceof Error ? err.message : "Failed to fetch biodiversity territories");
      setBiodiversityTerritories({} as BiodiversityTerritory);
    } finally {
      setIsLoadingBiodiversityTerritories(false);
    }
  };

  return {
    fetchBiodiversityTerritories,
    setSelectedTerritory,
  };
};
