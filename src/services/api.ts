import { IPointsResponse, IValidateCodeResponse, LeaderboardResponse } from "@/types";
import { makeRequest } from "@/utils/make-request";

export async function postUserPoints(jwt: string) {
  const { result, error } = await makeRequest<any>(
    {
      method: "POST",
      headers: {
        Authorization: "Bearer " + jwt,
      },
    },
    "/api/points/track",
  );

  if (error) {
    return;
  }

  return result;
}

export async function postValidateInviteCode(code: string) {
  const tokenFromStorage = JSON.parse(localStorage.getItem("privy:token") as string);
  const { result, error } = await makeRequest<IValidateCodeResponse>(
    {
      method: "POST",
      body: JSON.stringify({ code: code }),
      headers: {
        Authorization: "Bearer " + tokenFromStorage,
      },
    },
    "/api/referrals/validate",
  );

  if (error) {
    return;
  }

  return result;
}

export async function getForestData(jwt: string) {
  const { result, error } = await makeRequest<IValidateCodeResponse>(
    {
      method: "GET",
      headers: {
        Authorization: "Bearer " + jwt,
      },
    },
    "/api/territories/map/forest",
  );

  if (error) {
    return;
  }

  return result;
}

export async function getAlertsData(jwt: string) {
  const { result, error } = await makeRequest<IValidateCodeResponse>(
    {
      method: "GET",
      headers: {
        Authorization: "Bearer " + jwt,
      },
    },
    "/api/territories/map/alerts",
  );

  if (error) {
    return;
  }

  return result;
}

export async function getCountriesBoundariesData(jwt: string) {
  const { result, error } = await makeRequest<IValidateCodeResponse>(
    {
      method: "GET",
      headers: {
        Authorization: "Bearer " + jwt,
      },
    },
    "/api/territories/v2/boundaries",
  );

  if (error) {
    return;
  }

  return result;
}

export async function getStatesFromCountryBoundariesData(jwt: string, countryId: string) {
  const { result, error } = await makeRequest<IValidateCodeResponse>(
    {
      method: "GET",
      headers: {
        Authorization: "Bearer " + jwt,
      },
    },
    `/api/territories/v2/boundaries?country=${countryId}`,
  );

  if (error) {
    return;
  }

  return result;
}

export async function postPointEvent(
  jwt: string,
  event: {
    event: string;
    points: number;
  },
) {
  const { result, error } = await makeRequest<IValidateCodeResponse>(
    {
      method: "POST",
      body: JSON.stringify(event),
      headers: {
        Authorization: "Bearer " + jwt,
      },
    },
    "/api/points/track",
  );

  if (error) {
    return;
  }

  return result;
}

export async function getPointsEvent(jwt: string, account: string) {
  const { result, error } = await makeRequest<IPointsResponse>(
    {
      method: "GET",
      headers: {
        Authorization: "Bearer " + jwt,
      },
    },
    `/api/points?account=${account}`,
  );

  if (error) {
    return;
  }

  return result;
}

export async function getPointLeaderboard(jwt: string, page = 1, take = 10) {
  const { result, error } = await makeRequest<LeaderboardResponse>(
    {
      method: "GET",
      headers: {
        Authorization: "Bearer " + jwt,
      },
    },
    `/api/points/leaderboard?page=${page}&take=${take}`,
  );

  if (error) {
    return;
  }

  return result;
}
