import { api } from "@/lib/axios";
import { ClaimedProperties, MapLayerResponse, PropertyDetails } from "@/types";

export const territoriesService = {
  getForestData: () => api.get<MapLayerResponse>("/api/territories/map/forest"),

  getAlertsData: () => api.get<MapLayerResponse>("/api/territories/map/alerts"),

  getPropertyDetails: (id: string, lat: number, lng: number) =>
    api.get<PropertyDetails>(`/api/properties/details`, { params: { id, lat, lng } }).then((res) => ({
      ...res.data,
      totalForestArea: Number(res.data.totalForestArea),
    })),

  claimProperty: (id: string) => api.post(`/api/properties/claim`, { propertyId: id }),

  getClaimedProperties: () => api.get<ClaimedProperties>("/api/properties/claimed"),

  getCoordinates: (ibicode: string) => api.get(`/api/properties/${ibicode}/coordinates`),
};
