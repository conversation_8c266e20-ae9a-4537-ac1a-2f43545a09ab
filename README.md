# PlantYourTree Web

![ybycash-pyt](https://ik.imagekit.io/97q72hphb/Union.png?updatedAt=1744141959581)

This documentation provides general information about the app. Scroll through each section to find the details you're looking for. Please note that the app is still under development, and major changes that may directly impact the documentation will be implemented.

## Running locally

### Node Version

We recommend using Node.js version `v18.17.0` for this app, as newer versions may cause issues due to inconsistencies in Next.js. For more details, refer to the [Next.js discussion](https://github.com/vercel/next.js/discussions/57066).

### Makefile

This project uses a Makefile to manage its scripts. For additional information about the available scripts, please refer to the `Makefile` file on the root of the app.

### Commitizen

This project uses Commitizen to standardize commit messages, ensuring they follow a consistent and readable format. Commitizen helps keep the project's history organized, making it easier to read and understand the changes made over time. For more information on how to use Commitizen, refer to the project's documentation.

### pnpm Usage

This project uses [pnpm](https://pnpm.io/pt/). Please ensure that it is installed on your machine.

To install the project's dependencies, use:

```bash
make i
```

To run this project locally on your machine, use:

```bash
make d
```

To commit your local changes, use:

```bash
make c
```

If you want to build this app for production, use:

```bash
make prod
```

To reinstall dependencies and rerun the project, use:

```bash
make restart
```

To run globall code formatting with prettier, use:

```bash
make f
```

## Folder Structure

This project adheres to the Next.js App Router folder structure. For more details, please refer to the [Next.js documentation](https://nextjs.org/docs/). You can see more information about the current available routes on the app.

    .
    ├── ...
    ├── app  # The main app route
    │   ├── (auth)  # The grouped route that holds the authenticated area
             ├── world  # The world area that renders the main map
             ├── dashboard  # The user dashboard area
             ├── profile  # A route for the user profile
             ├── settings  # General user settings
    └── ...

## UI Components Structure and Styling

This project uses [Tailwind CSS](https://tailwindcss.com/docs) for styling and Shadcn for generating components. Please note that the components will undergo significant changes as design updates are implemented. For more information about Shadcn, refer to the [documentation here](https://shadcn.dev/docs).

## Authentication with Privy.io

This app uses [Privy.io](https://privy.io/docs) for handling user authentication and wallet generation. Backend integration will be used to store more complex user information. For more details about Privy, please refer to the [documentation](https://privy.io/docs).

## User Points Tracking with Stack.so

This app uses [Stack.so](https://docs.stack.so/overview.html) to track the current user points. For more information about Stack, please refer to the [documentation](https://docs.stack.so/overview.html).

## State Management

This app uses [Zustand](https://github.com/pmndrs/zustand) for state management. Zustand is a fast and flexible state management library for React, providing a simple and intuitive API for managing global state. It allows for easy state updates and efficient re-renders, helping to keep your application responsive and maintainable.

For more information on how to use Zustand and its features, please refer to the [documentation](https://github.com/pmndrs/zustand).

## Icon Component

For using icons, refer to the [Iconify documentation](https://iconify.design/) or browse their extensive collection of icons at [Iconify Icon Sets](https://icon-sets.iconify.design/).

You can easily import and use any icon with the following code:

```jsx
import { Icon } from "@iconify/react";

<Icon icon="ph:user-bold" />;
```
