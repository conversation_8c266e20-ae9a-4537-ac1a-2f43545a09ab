# Use the official Node.js image as the base
FROM node:18-alpine

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and package-lock.json (if available)
COPY package*.json ./

# Install project dependencies
RUN yarn install

# Copy project files to the container
COPY . .

# Build the application for production
RUN yarn build

# Expose the port that Next.js uses
EXPOSE 3000

# Set the command to start the application
CMD ["npm", "start"]